"use client"

import { useState, useEffect } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { ProductFilters } from "@/components/storefront/products/product-filters"
import { ProductSort } from "@/components/storefront/products/product-sort"
import { MobileFilters } from "@/components/mobile-filters"
import { Skeleton } from "@/components/ui/skeleton"
import { useNewArrivals } from "@/lib/ecommerce/hooks/use-collection-products"
import { transformToStorefrontProducts } from "@/lib/ecommerce/utils/product-transformers"
import { Sparkles, Loader2 } from "lucide-react"

export default function NewArrivalsPage() {
      const [sortBy, setSortBy] = useState("newest")
  const [filters, setFilters] = useState({
    category: "",
    color: "",
    size: "",
  })

  const { products: ecommerceProducts, loading, error } = useNewArrivals({
    sortBy,
    filters: {
      // Convert legacy filters to e-commerce format
      ...(filters.category && { categoryIds: [filters.category] }),
      // Additional filters can be added here
    },
    limit: 20
  })

  // Transform e-commerce products to storefront format
  const products = transformToStorefrontProducts(ecommerceProducts)

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Clean header */}
      <div className="mb-8">
        <h1 className="text-2xl font-normal mb-2">New Arrivals</h1>
        <p className="text-gray-600 text-sm">
          {products.length} products
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Desktop Filters Sidebar - Hidden on Mobile */}
        <div className="hidden lg:block lg:w-64 flex-shrink-0">
          <ProductFilters onFilterChange={handleFilterChange} />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Sort and Results Count */}
          <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
            <p className="text-sm text-gray-600">
              {loading ? "Loading..." : `${products.length} products`}
            </p>
            <div className="flex items-center gap-2">
              {/* Mobile Filter Button */}
              <div className="lg:hidden">
                <MobileFilters
                  selectedCategory={filters.category}
                  selectedColor={filters.color}
                  selectedSize={filters.size}
                />
              </div>
              <ProductSort value={sortBy} onValueChange={setSortBy} />
            </div>
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="aspect-[3/4] w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-16">
              <Sparkles className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Error loading new arrivals</h3>
              <p className="text-gray-600">
                {error.message}
              </p>
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <Sparkles className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No new arrivals found</h3>
              <p className="text-gray-600">
                Try adjusting your filters or check back soon for new products.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
