"use client"

import { useEffect, useState } from "react"
import { useWishlist } from "@/lib/ecommerce/hooks/use-wishlist"
import { useAuth } from "@/lib/ecommerce/hooks/use-auth"
import { ProductCard } from "@/components/storefront/products/product-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Heart, Loader2, AlertCircle, RefreshCw } from "lucide-react"
import Link from "next/link"
import { transformToStorefrontProducts } from "@/lib/ecommerce/utils/product-transformers"

export default function WishlistPage() {
  const { user } = useAuth()
  const {
    items,
    loading,
    error,
    removeFromWishlist,
    refetch,
    clearError
  } = useWishlist({
    userId: user?.id,
    autoFetch: true
  })

  const [retryCount, setRetryCount] = useState(0)

  // Transform wishlist items to products for display
  const products = items
    .filter(item => item.product) // Only items with loaded product data
    .map(item => item.product!)

  const storefrontProducts = transformToStorefrontProducts(products)

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    clearError()
    refetch()
  }

  const handleRemoveFromWishlist = async (productId: string) => {
    try {
      await removeFromWishlist(productId)
    } catch (error) {
      console.error("Failed to remove from wishlist:", error)
    }
  }

  // Loading state
  if (loading) {
    return (
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-6">My Wishlist</h1>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="aspect-[3/4] w-full rounded-lg" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-6">My Wishlist</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load wishlist: {error.message}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Empty state
  if (items.length === 0 || storefrontProducts.length === 0) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <Heart className="h-16 w-16 text-muted-foreground" />
          <h1 className="text-2xl font-bold font-montserrat">Your wishlist is empty</h1>
          <p className="text-muted-foreground">
            {!user
              ? "Sign in to save your favorite items and access them from any device."
              : "Looks like you haven't added anything to your wishlist yet."
            }
          </p>
          <div className="flex gap-4">
            <Button asChild>
              <Link href="/products">Continue Shopping</Link>
            </Button>
            {!user && (
              <Button variant="outline" asChild>
                <Link href="/auth/login">Sign In</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl md:text-3xl font-bold font-montserrat">My Wishlist</h1>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Heart className="h-4 w-4" />
          <span>{items.length} item{items.length !== 1 ? 's' : ''}</span>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
        {storefrontProducts.map((product) => (
          <div key={product.id} className="relative group">
            <Button
              variant="outline"
              size="icon"
              className="absolute right-2 top-2 z-10 h-8 w-8 rounded-full bg-white shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => handleRemoveFromWishlist(product.id)}
            >
              <Heart className="h-4 w-4 fill-current text-red-500" />
              <span className="sr-only">Remove from wishlist</span>
            </Button>
            <ProductCard product={product} />
          </div>
        ))}
      </div>

      {/* Additional actions */}
      <div className="mt-8 text-center">
        <Button variant="outline" onClick={refetch}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Wishlist
        </Button>
      </div>
    </div>
  )
}
