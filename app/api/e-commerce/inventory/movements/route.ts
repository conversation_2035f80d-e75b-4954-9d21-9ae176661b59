import { NextResponse } from 'next/server'
import { inventoryService } from '@/lib/ecommerce/services/inventory-service'

export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const inventoryItemId = url.searchParams.get('inventoryItemId')

    if (!inventoryItemId) {
      return NextResponse.json(
        { success: false, error: 'inventoryItemId is required' },
        { status: 400 }
      )
    }

    const result = await inventoryService.getInventoryMovements(inventoryItemId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error?.message || 'Failed to fetch movements' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data
    })
  } catch (error) {
    console.error('Error fetching inventory movements:', error)
    return NextResponse.json(
      { success: false, error: (error as Error).message },
      { status: 500 }
    )
  }
}