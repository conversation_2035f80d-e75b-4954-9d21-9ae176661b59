import { openai } from '@ai-sdk/openai'
import { streamText, tool } from 'ai'
import { z } from 'zod'
import { generateObject } from 'ai'
import { analyzeComponentStructure, generateComponentId, validateComponentCode, sanitizeComponentCode } from '@/lib/ai-visual-editor/utils/component-analyzer'
import { generatePropertiesSchema, generateDefaultValues } from '@/lib/ai-visual-editor/utils/properties-generator'
import { analyzeComponentAdvanced } from '@/lib/ai-visual-editor/utils/advanced-component-analyzer'
import { NextRequest } from 'next/server'

export const maxDuration = 30

// Rate limiting and security
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_MAX = 10 // requests per window
const RATE_LIMIT_WINDOW = 60000 // 1 minute

function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const userLimit = rateLimitMap.get(ip)

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (userLimit.count >= RATE_LIMIT_MAX) {
    return false
  }

  userLimit.count++
  return true
}

const componentSchema = z.object({
  name: z.string(),
  description: z.string(),
  category: z.enum(['layout', 'content', 'media', 'form', 'navigation', 'data']),
  jsx: z.string(),
  imports: z.array(z.string()).optional(),
  dependencies: z.array(z.string()).optional()
})

export async function POST(req: NextRequest) {
  try {
    // Rate limiting
    const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown'
    if (!checkRateLimit(ip)) {
      return new Response(
        JSON.stringify({ error: 'Rate limit exceeded', message: 'Too many requests' }),
        { status: 429, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Input validation
    const body = await req.json()
    if (!body.messages || !Array.isArray(body.messages)) {
      return new Response(
        JSON.stringify({ error: 'Invalid input', message: 'Messages array is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const { messages } = body

    // Enhanced error handling and monitoring
    const startTime = Date.now()

    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      maxSteps: 5,
      onFinish: (result) => {
        const duration = Date.now() - startTime
        console.log(`[AI Editor] Request completed in ${duration}ms, tokens: ${result.usage?.totalTokens || 'unknown'}`)
      },
      tools: {
        generateComponent: tool({
          description: 'Generate a React component with TypeScript and Tailwind CSS using advanced AI analysis',
          parameters: z.object({
            description: z.string().describe('Description of the component to generate'),
            componentType: z.enum(['hero', 'card', 'button', 'form', 'navigation', 'footer', 'custom']),
            features: z.array(z.string()).describe('Specific features or functionality needed'),
            styling: z.enum(['modern', 'minimal', 'bold', 'elegant', 'playful']).default('modern'),
            complexity: z.enum(['simple', 'moderate', 'complex']).default('moderate'),
            accessibility: z.boolean().default(true),
            responsive: z.boolean().default(true)
          }),
          execute: async ({ description, componentType, features, styling, complexity, accessibility, responsive }) => {
            try {
              const startTime = Date.now()

              const { object } = await generateObject({
                model: openai('gpt-4o'),
                schema: componentSchema,
                prompt: `
                  Generate a high-quality React component with the following specifications:
                  - Description: ${description}
                  - Type: ${componentType}
                  - Features: ${features.join(', ')}
                  - Styling: ${styling}
                  - Complexity: ${complexity}
                  - Accessibility: ${accessibility ? 'Required' : 'Optional'}
                  - Responsive: ${responsive ? 'Required' : 'Optional'}

                  Requirements:
                  1. Create a functional React component using TypeScript
                  2. Use Tailwind CSS for styling with ${styling} design approach
                  3. Use shadcn/ui components when appropriate
                  4. ${responsive ? 'Make it fully responsive with mobile-first design' : 'Focus on desktop design'}
                  5. ${accessibility ? 'Include comprehensive accessibility features (ARIA labels, semantic HTML, keyboard navigation)' : 'Basic accessibility'}
                  6. Include proper TypeScript interfaces for props
                  7. Add appropriate hover states and transitions
                  8. Follow modern React patterns and best practices
                  9. Ensure component is performant and optimized
                  10. Include error boundaries where appropriate

                  Complexity guidelines:
                  - Simple: Basic structure, minimal interactions
                  - Moderate: Some state management, multiple sections
                  - Complex: Advanced interactions, multiple components, data handling

                  Component should be production-ready and follow industry standards.

                  Example structure:
                  \`\`\`tsx
                  interface ComponentNameProps {
                    title?: string
                    description?: string
                    className?: string
                    // other props based on features
                  }

                  export function ComponentName({
                    title = "Default Title",
                    description,
                    className = "",
                    ...props
                  }: ComponentNameProps) {
                    return (
                      <div className={\`base-classes \${className}\`} {...props}>
                        {/* component content with proper structure */}
                      </div>
                    )
                  }
                  \`\`\`
                `
              })

              // Enhanced validation
              const validation = validateComponentCode(object.jsx)
              if (!validation.isValid) {
                throw new Error(`Code validation failed: ${validation.errors.join(', ')}`)
              }

              const sanitizedCode = sanitizeComponentCode(object.jsx)

              // Advanced component analysis
              const advancedAnalysis = analyzeComponentAdvanced(sanitizedCode)

              // Generate enhanced properties configuration
              const propertiesConfig = generatePropertiesSchema(advancedAnalysis)
              const defaultValues = generateDefaultValues(propertiesConfig)

              // Performance estimation
              const generationTime = Date.now() - startTime

              // Create enhanced component object
              const component = {
                id: generateComponentId(),
                name: object.name,
                description: object.description,
                category: object.category,
                jsx: sanitizedCode,
                props: {},
                propertiesConfig,
                defaultValues,
                createdAt: new Date(),
                updatedAt: new Date(),
                metadata: {
                  complexity: advancedAnalysis.complexity,
                  interactivity: advancedAnalysis.interactivity,
                  dataRequirements: advancedAnalysis.dataRequirements,
                  accessibilityFeatures: advancedAnalysis.accessibilityFeatures,
                  performanceScore: Math.max(0, 100 - generationTime / 10),
                  estimatedRenderTime: advancedAnalysis.estimatedRenderTime,
                  dependencies: advancedAnalysis.dependencies,
                  generationTime
                }
              }

              return {
                success: true,
                component,
                analysis: advancedAnalysis,
                message: `Generated ${object.name} component (${advancedAnalysis.complexity} complexity) with ${Object.keys(propertiesConfig).length} property sections`,
                performance: {
                  generationTime,
                  estimatedRenderTime: advancedAnalysis.estimatedRenderTime,
                  optimizations: advancedAnalysis.suggestedOptimizations
                }
              }
            } catch (error) {
              console.error('Component generation error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to generate component',
                message: 'Component generation failed. Please try a simpler description or different approach.',
                suggestions: [
                  'Try reducing the complexity level',
                  'Simplify the feature requirements',
                  'Use more specific component type',
                  'Check if the description is clear and actionable'
                ]
              }
            }
          }
        }),

        analyzeComponent: tool({
          description: 'Analyze an existing component and generate properties configuration',
          parameters: z.object({
            componentCode: z.string().describe('React component code to analyze'),
            componentName: z.string().describe('Name of the component'),
            focusArea: z.enum(['appearance', 'content', 'behavior', 'data', 'all']).default('all')
          }),
          execute: async ({ componentCode, componentName, focusArea }) => {
            try {
              const analysis = analyzeComponentStructure(componentCode)
              const propertiesConfig = generatePropertiesSchema(analysis)
              const defaultValues = generateDefaultValues(propertiesConfig)

              return {
                success: true,
                componentName,
                propertiesConfig,
                defaultValues,
                analysis: analysis.summary,
                message: `Analyzed ${componentName} and generated ${Object.keys(propertiesConfig).length} property sections`
              }
            } catch (error) {
              console.error('Component analysis error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to analyze component',
                message: 'Component analysis failed. Please check the component code.'
              }
            }
          }
        }),

        optimizeComponent: tool({
          description: 'Optimize an existing component for performance, accessibility, or styling',
          parameters: z.object({
            componentCode: z.string().describe('Current component code'),
            optimizationType: z.enum(['performance', 'accessibility', 'responsive', 'styling']),
            specificRequirements: z.string().optional().describe('Specific optimization requirements')
          }),
          execute: async ({ componentCode, optimizationType, specificRequirements }) => {
            try {
              const { object } = await generateObject({
                model: openai('gpt-4o'),
                schema: z.object({
                  optimizedCode: z.string(),
                  changes: z.array(z.string()),
                  improvements: z.string()
                }),
                prompt: `
                  Optimize the following React component for ${optimizationType}:
                  
                  ${componentCode}
                  
                  ${specificRequirements ? `Specific requirements: ${specificRequirements}` : ''}
                  
                  Focus on:
                  ${optimizationType === 'performance' ? '- Memoization, lazy loading, efficient rendering' : ''}
                  ${optimizationType === 'accessibility' ? '- ARIA labels, keyboard navigation, screen reader support' : ''}
                  ${optimizationType === 'responsive' ? '- Mobile-first design, flexible layouts, breakpoints' : ''}
                  ${optimizationType === 'styling' ? '- Better visual hierarchy, spacing, colors, typography' : ''}
                  
                  Return the optimized code and list the changes made.
                `
              })

              const validation = validateComponentCode(object.optimizedCode)
              if (!validation.isValid) {
                throw new Error(`Optimized code validation failed: ${validation.errors.join(', ')}`)
              }

              return {
                success: true,
                optimizedCode: sanitizeComponentCode(object.optimizedCode),
                changes: object.changes,
                improvements: object.improvements,
                message: `Optimized component for ${optimizationType} with ${object.changes.length} improvements`
              }
            } catch (error) {
              console.error('Component optimization error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to optimize component',
                message: 'Component optimization failed. Please try again.'
              }
            }
          }
        })
      }
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('API route error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: 'Failed to process request'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
