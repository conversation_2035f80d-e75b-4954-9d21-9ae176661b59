import { openai } from '@ai-sdk/openai'
import { streamText, tool } from 'ai'
import { z } from 'zod'
import { generateObject } from 'ai'
import { NextJSGenerator } from '@/lib/ai-visual-editor/services/nextjs-generator'
import { NextRequest } from 'next/server'

export const maxDuration = 60

// Enhanced schemas for Next.js generation
const nextjsLayoutSchema = z.object({
  name: z.string(),
  description: z.string(),
  type: z.enum(['root', 'nested', 'template', 'group']),
  components: z.array(z.object({
    name: z.string(),
    type: z.string(),
    jsx: z.string(),
    props: z.record(z.any()).optional()
  })),
  metadata: z.object({
    title: z.string(),
    description: z.string(),
    keywords: z.array(z.string()).optional()
  }).optional(),
  features: z.array(z.string())
})

const nextjsPageSchema = z.object({
  name: z.string(),
  description: z.string(),
  route: z.string(),
  type: z.enum(['static', 'dynamic', 'catch-all', 'optional-catch-all']),
  pageType: z.enum(['landing', 'product', 'blog', 'dashboard', 'auth', 'error', 'custom']),
  components: z.array(z.object({
    name: z.string(),
    type: z.string(),
    jsx: z.string(),
    props: z.record(z.any()).optional()
  })),
  metadata: z.object({
    title: z.string(),
    description: z.string(),
    keywords: z.array(z.string()).optional()
  }).optional(),
  features: z.array(z.string())
})

export async function POST(req: NextRequest) {
  try {
    const { messages } = await req.json()

    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      maxSteps: 5,
      tools: {
        generateNextJSLayout: tool({
          description: 'Generate a complete Next.js layout with components and metadata',
          parameters: z.object({
            description: z.string().describe('Description of the layout to generate'),
            layoutType: z.enum(['root', 'nested', 'template', 'group']).default('root'),
            features: z.array(z.string()).describe('Features to include (header, footer, sidebar, etc.)'),
            styling: z.enum(['modern', 'minimal', 'bold', 'elegant', 'playful']).default('modern'),
            includeHeader: z.boolean().default(true),
            includeFooter: z.boolean().default(true),
            includeSidebar: z.boolean().default(false),
            responsive: z.boolean().default(true),
            accessibility: z.boolean().default(true),
            seo: z.boolean().default(true)
          }),
          execute: async ({ 
            description, 
            layoutType, 
            features, 
            styling, 
            includeHeader, 
            includeFooter, 
            includeSidebar,
            responsive,
            accessibility,
            seo
          }) => {
            try {
              const { object } = await generateObject({
                model: openai('gpt-4o'),
                schema: nextjsLayoutSchema,
                prompt: `
                  Generate a complete Next.js layout with the following specifications:
                  - Description: ${description}
                  - Layout Type: ${layoutType}
                  - Features: ${features.join(', ')}
                  - Styling: ${styling}
                  - Include Header: ${includeHeader}
                  - Include Footer: ${includeFooter}
                  - Include Sidebar: ${includeSidebar}
                  - Responsive: ${responsive}
                  - Accessibility: ${accessibility}
                  - SEO Optimized: ${seo}
                  
                  Requirements:
                  1. Create a complete Next.js layout using the app router
                  2. Use TypeScript and modern React patterns
                  3. Use Tailwind CSS for styling with ${styling} design approach
                  4. Include proper metadata configuration if SEO is enabled
                  5. Make it responsive and accessible if specified
                  6. Include semantic HTML elements
                  7. Generate individual components for header, footer, sidebar if requested
                  8. Follow Next.js 14+ best practices
                  9. Include proper error boundaries and loading states
                  10. Use shadcn/ui components where appropriate
                  
                  Layout types:
                  - root: Main app layout (app/layout.tsx)
                  - nested: Nested layout for specific routes
                  - template: Template that re-renders on navigation
                  - group: Route group layout for organization
                  
                  Generate complete, production-ready code with proper imports and exports.
                `
              })

              // Generate the actual layout using our service
              const layoutParams = {
                name: object.name,
                description: object.description,
                type: object.type,
                features: object.features,
                styling,
                includeHeader,
                includeFooter,
                includeSidebar,
                responsive,
                accessibility,
                seo
              }

              const generatedLayout = await NextJSGenerator.generateLayout(layoutParams)

              return {
                success: true,
                layout: generatedLayout,
                generatedComponents: object.components,
                message: `Generated ${object.name} layout with ${object.components.length} components`,
                features: object.features,
                path: generatedLayout.path
              }
            } catch (error) {
              console.error('Next.js layout generation error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to generate layout',
                message: 'Layout generation failed. Please try again with a different description.'
              }
            }
          }
        }),

        generateNextJSPage: tool({
          description: 'Generate a complete Next.js page with components and routing',
          parameters: z.object({
            description: z.string().describe('Description of the page to generate'),
            route: z.string().describe('Route path (e.g., /, /products, /blog/[slug])'),
            pageType: z.enum(['landing', 'product', 'blog', 'dashboard', 'auth', 'error', 'custom']).default('custom'),
            routeType: z.enum(['static', 'dynamic', 'catch-all', 'optional-catch-all']).default('static'),
            features: z.array(z.string()).describe('Features to include'),
            styling: z.enum(['modern', 'minimal', 'bold', 'elegant', 'playful']).default('modern'),
            seo: z.boolean().default(true),
            generateStaticParams: z.boolean().default(false),
            revalidate: z.number().optional()
          }),
          execute: async ({ 
            description, 
            route, 
            pageType, 
            routeType, 
            features, 
            styling, 
            seo, 
            generateStaticParams,
            revalidate
          }) => {
            try {
              const { object } = await generateObject({
                model: openai('gpt-4o'),
                schema: nextjsPageSchema,
                prompt: `
                  Generate a complete Next.js page with the following specifications:
                  - Description: ${description}
                  - Route: ${route}
                  - Page Type: ${pageType}
                  - Route Type: ${routeType}
                  - Features: ${features.join(', ')}
                  - Styling: ${styling}
                  - SEO Optimized: ${seo}
                  - Generate Static Params: ${generateStaticParams}
                  - Revalidate: ${revalidate || 'none'}
                  
                  Requirements:
                  1. Create a complete Next.js page using the app router
                  2. Use TypeScript and modern React patterns
                  3. Use Tailwind CSS for styling with ${styling} design approach
                  4. Include proper metadata configuration if SEO is enabled
                  5. Handle dynamic routes properly if specified
                  6. Include generateStaticParams if requested
                  7. Add proper loading and error states
                  8. Use shadcn/ui components where appropriate
                  9. Follow Next.js 14+ best practices
                  10. Make it responsive and accessible
                  
                  Page types and their typical components:
                  - landing: Hero, Features, Testimonials, CTA
                  - product: Gallery, Details, Reviews, Related Products
                  - blog: Header, Content, Sidebar, Related Posts
                  - dashboard: Metrics, Charts, Tables, Navigation
                  - auth: Login/Register forms, Social auth, Validation
                  - error: Error message, Navigation, Suggestions
                  
                  Route types:
                  - static: Regular static route
                  - dynamic: [param] dynamic segment
                  - catch-all: [...param] catches all segments
                  - optional-catch-all: [[...param]] optional catch-all
                  
                  Generate complete, production-ready code with proper imports and exports.
                `
              })

              // Generate the actual page using our service
              const pageParams = {
                name: object.name,
                description: object.description,
                route: object.route,
                type: object.type,
                pageType: object.pageType,
                features: object.features,
                styling,
                seo,
                generateStaticParams,
                revalidate
              }

              const generatedPage = await NextJSGenerator.generatePage(pageParams)

              return {
                success: true,
                page: generatedPage,
                generatedComponents: object.components,
                message: `Generated ${object.name} page with ${object.components.length} components`,
                features: object.features,
                route: object.route,
                filePath: this.generatePageFilePath(object.route, object.type)
              }
            } catch (error) {
              console.error('Next.js page generation error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to generate page',
                message: 'Page generation failed. Please try again with a different description.'
              }
            }
          }
        }),

        generateFullNextJSProject: tool({
          description: 'Generate a complete Next.js project structure with multiple layouts and pages',
          parameters: z.object({
            projectName: z.string().describe('Name of the project'),
            description: z.string().describe('Project description'),
            projectType: z.enum(['ecommerce', 'blog', 'saas', 'portfolio', 'corporate', 'custom']),
            features: z.array(z.string()).describe('Project features'),
            styling: z.enum(['modern', 'minimal', 'bold', 'elegant', 'playful']).default('modern'),
            includeAuth: z.boolean().default(false),
            includeBlog: z.boolean().default(false),
            includeEcommerce: z.boolean().default(false),
            includeDashboard: z.boolean().default(false)
          }),
          execute: async ({ 
            projectName, 
            description, 
            projectType, 
            features, 
            styling,
            includeAuth,
            includeBlog,
            includeEcommerce,
            includeDashboard
          }) => {
            try {
              // Generate project structure based on type
              const layouts = []
              const pages = []

              // Root layout
              layouts.push({
                name: 'Root Layout',
                description: 'Main application layout',
                type: 'root' as const,
                features: ['header', 'footer'],
                styling,
                includeHeader: true,
                includeFooter: true,
                includeSidebar: false,
                responsive: true,
                accessibility: true,
                seo: true
              })

              // Home page
              pages.push({
                name: 'Home Page',
                description: 'Main landing page',
                route: '/',
                type: 'static' as const,
                pageType: 'landing' as const,
                features: ['hero', 'features', 'cta'],
                styling,
                seo: true,
                generateStaticParams: false
              })

              // Add conditional pages based on features
              if (includeAuth) {
                pages.push({
                  name: 'Login Page',
                  description: 'User authentication page',
                  route: '/login',
                  type: 'static' as const,
                  pageType: 'auth' as const,
                  features: ['login-form', 'social-auth'],
                  styling,
                  seo: true,
                  generateStaticParams: false
                })
              }

              if (includeBlog) {
                pages.push({
                  name: 'Blog Page',
                  description: 'Blog listing page',
                  route: '/blog',
                  type: 'static' as const,
                  pageType: 'blog' as const,
                  features: ['post-list', 'pagination', 'search'],
                  styling,
                  seo: true,
                  generateStaticParams: false
                })
              }

              if (includeEcommerce) {
                pages.push({
                  name: 'Products Page',
                  description: 'Product listing page',
                  route: '/products',
                  type: 'static' as const,
                  pageType: 'product' as const,
                  features: ['product-grid', 'filters', 'pagination'],
                  styling,
                  seo: true,
                  generateStaticParams: false
                })
              }

              if (includeDashboard) {
                layouts.push({
                  name: 'Dashboard Layout',
                  description: 'Dashboard layout with sidebar',
                  type: 'nested' as const,
                  features: ['sidebar', 'header'],
                  styling,
                  includeHeader: true,
                  includeFooter: false,
                  includeSidebar: true,
                  responsive: true,
                  accessibility: true,
                  seo: false
                })

                pages.push({
                  name: 'Dashboard Page',
                  description: 'Main dashboard page',
                  route: '/dashboard',
                  type: 'static' as const,
                  pageType: 'dashboard' as const,
                  features: ['metrics', 'charts', 'tables'],
                  styling,
                  seo: false,
                  generateStaticParams: false
                })
              }

              // Generate the complete project
              const project = await NextJSGenerator.generateProject(layouts, pages)

              return {
                success: true,
                project,
                message: `Generated complete ${projectName} project with ${project.layouts.length} layouts and ${project.pages.length} pages`,
                structure: {
                  layouts: project.layouts.length,
                  pages: project.pages.length,
                  components: project.components.length,
                  routes: project.routes
                }
              }
            } catch (error) {
              console.error('Next.js project generation error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to generate project',
                message: 'Project generation failed. Please try again with different parameters.'
              }
            }
          }
        })
      }
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('API route error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: 'Failed to process request'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// Helper function to generate file path for pages
function generatePageFilePath(route: string, type: string): string {
  if (route === '/') return 'app/page.tsx'
  
  // Handle dynamic routes
  if (type === 'dynamic') {
    return `app${route}/page.tsx`
  }
  
  // Handle catch-all routes
  if (type === 'catch-all') {
    const segments = route.split('/').filter(Boolean)
    const lastSegment = segments[segments.length - 1]
    const basePath = segments.slice(0, -1).join('/')
    return `app/${basePath}/[...${lastSegment}]/page.tsx`
  }
  
  // Handle optional catch-all routes
  if (type === 'optional-catch-all') {
    const segments = route.split('/').filter(Boolean)
    const lastSegment = segments[segments.length - 1]
    const basePath = segments.slice(0, -1).join('/')
    return `app/${basePath}/[[...${lastSegment}]]/page.tsx`
  }
  
  // Static routes
  return `app${route}/page.tsx`
}
