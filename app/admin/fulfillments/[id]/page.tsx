'use client'

import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'
import { toast } from 'sonner'
import { ArrowLeft, Package, Truck } from 'lucide-react'
import { useFulfillments, Fulfillment } from '@/lib/ecommerce/hooks/use-fulfillments'
import { OrderFulfillment } from '@/lib/ecommerce/types/order'

export default function FulfillmentDetailPage() {
  const params = useParams()
  const router = useRouter()
  const fulfillmentId = params.id as string

  const {
    getFulfillment,
    updateFulfillmentStatus,
    cancelFulfillment,
    trackFulfillment
  } = useFulfillments()

  const [fulfillment, setFulfillment] = useState<Fulfillment | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const [newStatus, setNewStatus] = useState<string>('')
  const [statusNotes, setStatusNotes] = useState<string>('')

  const [cancelReason, setCancelReason] = useState<string>('')

  // Helpers for badge
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary'
      case 'open':
        return 'default'
      case 'in_transit':
        return 'default'
      case 'delivered':
        return 'default'
      case 'cancelled':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const getStatusBadge = (status: string) => (
    <Badge variant={getStatusBadgeVariant(status)}>
      {status.replace('_', ' ')}
    </Badge>
  )

  // Fetch fulfillment on mount
  useEffect(() => {
    const fetch = async () => {
      setLoading(true)
      setError(null)
      const result = await getFulfillment(fulfillmentId)
      if (result.success) {
        setFulfillment(result.data)
        setNewStatus(result.data.status)
      } else {
        setError(result.error || 'Failed to load fulfillment')
      }
      setLoading(false)
    }
    fetch()
  }, [fulfillmentId])

  const handleRefreshTracking = async () => {
    if (!fulfillment) return
    setLoading(true)
    const result = await trackFulfillment(fulfillment.id)
    if (result.success) {
      toast.success('Tracking information updated')
      // reload details
      const r2 = await getFulfillment(fulfillment.id)
      if (r2.success) setFulfillment(r2.data)
    } else {
      toast.error(result.error || 'Failed to refresh tracking')
    }
    setLoading(false)
  }

  const handleUpdateStatus = async () => {
    if (!fulfillment) return
    setLoading(true)
    const result = await updateFulfillmentStatus(fulfillment.id, newStatus, statusNotes)
    if (result.success) {
      toast.success('Status updated')
      const r2 = await getFulfillment(fulfillment.id)
      if (r2.success) setFulfillment(r2.data)
      setStatusNotes('')
    } else {
      toast.error(result.error || 'Failed to update status')
    }
    setLoading(false)
  }

  const handleCancel = async () => {
    if (!fulfillment) return
    if (!cancelReason) {
      toast.error('Please provide a cancellation reason')
      return
    }
    setLoading(true)
    const result = await cancelFulfillment(fulfillment.id, cancelReason)
    if (result.success) {
      toast.success('Fulfillment cancelled')
      router.push('/admin/fulfillments')
    } else {
      toast.error(result.error || 'Failed to cancel fulfillment')
    }
    setLoading(false)
  }

  if (loading && !fulfillment) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/fulfillments">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Fulfillments
          </Link>
        </Button>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-40 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error || !fulfillment) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/fulfillments">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Fulfillments
          </Link>
        </Button>
        <Card>
          <CardContent className="p-6 text-center">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Fulfillment not found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {error || 'The fulfillment you are looking for does not exist.'}
            </p>
            <div className="mt-6">
              <Button asChild>
                <Link href="/admin/fulfillments">Back to Fulfillments</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumbs / Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/fulfillments">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Fulfillments
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Fulfillment for Order #{fulfillment.order.orderNumber}
            </h1>
            <p className="text-muted-foreground">ID: {fulfillment.id}</p>
          </div>
        </div>
        <div>{getStatusBadge(fulfillment.status)}</div>
      </div>

      {/* Items */}
      <Card>
        <CardHeader>
          <CardTitle>Items</CardTitle>
          <CardDescription>Products included in this fulfillment</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {fulfillment.items.map((item) => (
            <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="h-16 w-16 rounded-md bg-gray-100 flex items-center justify-center">
                  {item.orderItem.product.images?.[0] ? (
                    <img
                      src={item.orderItem.product.images[0]}
                      alt={item.orderItem.product.name}
                      className="h-16 w-16 rounded-md object-cover"
                    />
                  ) : (
                    <Package className="h-8 w-8 text-gray-400" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium">{item.orderItem.productTitle}</h4>
                  {item.orderItem.variantTitle && (
                    <p className="text-sm text-muted-foreground">{item.orderItem.variantTitle}</p>
                  )}
                  <p className="text-sm text-muted-foreground">
                    SKU: {item.orderItem.product.sku || 'N/A'}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">
                  {item.quantity} × {new Intl.NumberFormat('en-ZA', {
                    style: 'currency',
                    currency: fulfillment.order.currency
                  }).format(item.orderItem.unitPrice)}
                </p>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Tracking */}
      <Card>
        <CardHeader>
          <CardTitle>Tracking</CardTitle>
          <CardDescription>Shipping information and tracking</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Carrier</label>
              <Input value={fulfillment.carrier || ''} disabled />
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Service</label>
              <Input value={fulfillment.service || ''} disabled />
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Tracking Number</label>
              {fulfillment.trackingNumber ? (
                <a
                  href={fulfillment.trackingUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 underline"
                >
                  {fulfillment.trackingNumber}
                </a>
              ) : (
                <span className="text-sm text-muted-foreground">None</span>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Shipped At</label>
              <Input 
                value={fulfillment.shippedAt ? new Date(fulfillment.shippedAt).toLocaleString() : 'Not shipped'} 
                disabled 
              />
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Delivered At</label>
              <Input 
                value={fulfillment.deliveredAt ? new Date(fulfillment.deliveredAt).toLocaleString() : 'Not delivered'} 
                disabled 
              />
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshTracking}
          >
            <Truck className="mr-2 h-4 w-4" />
            Refresh Tracking
          </Button>
        </CardContent>
      </Card>

      {/* Status Update */}
      <Card>
        <CardHeader>
          <CardTitle>Update Status</CardTitle>
          <CardDescription>Change fulfillment status and add notes</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">Status</label>
            <Select value={newStatus} onValueChange={setNewStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="in_transit">In Transit</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Notes</label>
            <Input
              placeholder="Add notes (optional)"
              value={statusNotes}
              onChange={(e) => setStatusNotes(e.target.value)}
            />
          </div>
          <div className="flex items-end">
            <Button onClick={handleUpdateStatus} disabled={loading}>
              Update Status
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Notifications */}
      <Card>
        <CardHeader>
          <CardTitle>Notifications</CardTitle>
          <CardDescription>Customer notification settings and status</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">Notify Customer</label>
            <Input value={fulfillment.notifyCustomer ? 'Yes' : 'No'} disabled />
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Email Sent</label>
            <Input value={fulfillment.emailSent ? 'Yes' : 'No'} disabled />
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">SMS Sent</label>
            <Input value={fulfillment.smsSent ? 'Yes' : 'No'} disabled />
          </div>
        </CardContent>
      </Card>

      {/* Details */}
      <Card>
        <CardHeader>
          <CardTitle>Details</CardTitle>
          <CardDescription>Additional fulfillment information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Location</label>
              <Input value={fulfillment.locationName || 'Not specified'} disabled />
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Estimated Delivery</label>
              <Input 
                value={fulfillment.estimatedDeliveryAt ? new Date(fulfillment.estimatedDeliveryAt).toLocaleString() : 'Not specified'} 
                disabled 
              />
            </div>
          </div>
          
          {fulfillment.notes && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Notes</label>
              <div className="p-3 bg-gray-50 rounded-md">
                <p className="text-sm">{fulfillment.notes}</p>
              </div>
            </div>
          )}
          
          {fulfillment.receipt && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">Receipt Information</label>
              <div className="p-3 bg-gray-50 rounded-md space-y-2">
                {fulfillment.receipt.testCase && (
                  <p className="text-sm"><strong>Test Case:</strong> Yes</p>
                )}
                {fulfillment.receipt.authorization && (
                  <p className="text-sm"><strong>Authorization:</strong> {fulfillment.receipt.authorization}</p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cancel Fulfillment */}
      {fulfillment.status !== 'cancelled' && (
        <Card>
          <CardHeader>
            <CardTitle>Cancel Fulfillment</CardTitle>
            <CardDescription>Provide a reason to cancel this fulfillment</CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <label className="text-sm font-medium text-muted-foreground">Reason</label>
              <Input
                placeholder="Cancellation reason"
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
              />
            </div>
            <div className="flex items-end">
              <Button
                variant="destructive"
                onClick={handleCancel}
                disabled={loading}
              >
                Cancel Fulfillment
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
