'use client'

import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import { EnhancedOrderList } from '@/components/admin/orders/enhanced-order-list'
import { Order } from '@/lib/ecommerce/types'

export default function OrdersPage() {
  const router = useRouter()
  
  const { orders, loading, pagination, searchOrders } = useOrders({
    initialParams: {
      page: 1,
      limit: 20,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    },
    autoFetch: true
  })

  const handleCreateOrder = useCallback(() => {
    router.push('/admin/orders/new')
  }, [router])

  const handleEditOrder = useCallback((order: Order) => {
    router.push(`/admin/orders/${order.id}/edit`)
  }, [router])

  const handleViewOrder = useCallback((order: Order) => {
    router.push(`/admin/orders/${order.id}`)
  }, [router])

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Orders</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Enhanced Order List */}
      <EnhancedOrderList
        onCreateOrder={handleCreateOrder}
        onEditOrder={handleEditOrder}
        onViewOrder={handleViewOrder}
      />
    </div>
  )
}
