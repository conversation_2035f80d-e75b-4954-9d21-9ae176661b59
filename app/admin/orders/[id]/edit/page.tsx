'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm, useFieldArray } from 'react-hook-form'
import { useOrder } from '@/lib/ecommerce/hooks/use-orders'
import { useOrderMutations } from '@/lib/ecommerce/hooks/use-orders'
import { useCustomers } from '@/lib/ecommerce/hooks/use-customers'
import { useInventory } from '@/lib/ecommerce/hooks/use-inventory'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

type OrderFormValues = {
  customerId: string
  address: {
    firstName: string
    lastName: string
    line1: string
    line2?: string
    city: string
    state: string
    postalCode: string
    country: string
  }
  items: {
    inventoryItemId: string
    quantity: number
  }[]
}

export default function EditOrderPage({
  params,
}: {
  params: { id: string }
}) {
  const router = useRouter()
  const orderId = params.id

  const {
    order,
    loading: loadingOrder,
    error: orderError,
  } = useOrder({ orderId })

  const {
    updateOrder,
    loading: updatingOrder,
    error: updateError,
    clearError: clearUpdateError,
  } = useOrderMutations()

  const {
    customers,
    loading: loadingCustomers,
    error: customersError,
  } = useCustomers()

  const {
    inventory,
    loading: loadingInventory,
    error: inventoryError,
  } = useInventory()

  const {
    register,
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<OrderFormValues>({
    defaultValues: {
      customerId: '',
      address: {
        firstName: '',
        lastName: '',
        line1: '',
        line2: '',
        city: '',
        state: '',
        postalCode: '',
        country: '',
      },
      items: [{ inventoryItemId: '', quantity: 1 }],
    },
  })

  // When order data is loaded, populate form
  useEffect(() => {
    if (order) {
      reset({
        customerId: order.customerId,
        address: {
          firstName: order.shippingAddress.firstName,
          lastName: order.shippingAddress.lastName,
          line1: order.shippingAddress.line1,
          line2: order.shippingAddress.line2 || '',
          city: order.shippingAddress.city,
          state: order.shippingAddress.state,
          postalCode: order.shippingAddress.postalCode,
          country: order.shippingAddress.country,
        },
        items: order.items.map((item) => ({
          inventoryItemId: item.inventoryItemId,
          quantity: item.quantity,
        })),
      })
    }
  }, [order, reset])

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items',
  })

  const onSubmit = async (data: OrderFormValues) => {
    clearUpdateError()
    const input = {
      id: orderId,
      customerId: data.customerId,
      shippingAddress: {
        firstName: data.address.firstName,
        lastName: data.address.lastName,
        line1: data.address.line1,
        line2: data.address.line2,
        city: data.address.city,
        state: data.address.state,
        postalCode: data.address.postalCode,
        country: data.address.country,
      },
      lineItems: data.items.map((item) => ({
        inventoryItemId: item.inventoryItemId,
        quantity: item.quantity,
      })),
    }
    const updated = await updateOrder(input)
    if (updated && (updated as any).id) {
      router.push(`/admin/orders/${(updated as any).id}`)
    }
  }

  if (loadingOrder) {
    return <div>Loading order...</div>
  }

  if (orderError) {
    return <p className="text-red-600">{orderError}</p>
  }

  if (!order) {
    return <p>Order not found.</p>
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Customer Selector */}
      <div>
        <label className="block mb-1 font-medium">Customer</label>
        <select
          {...register('customerId', { required: 'Customer is required' })}
          disabled={loadingCustomers}
          className="block w-full rounded-md border px-3 py-2"
        >
          <option value="">Select a customer</option>
          {customers.map((c) => (
            <option key={c.id} value={c.id}>
              {c.email || `${c.firstName} ${c.lastName}`}
            </option>
          ))}
        </select>
        {errors.customerId && (
          <p className="text-sm text-red-600 mt-1">
            {errors.customerId.message}
          </p>
        )}
        {customersError && (
          <p className="text-sm text-red-600">{customersError}</p>
        )}
      </div>

      {/* Address Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block mb-1 font-medium">First Name</label>
          <Input
            {...register('address.firstName', {
              required: 'First name is required',
            })}
            placeholder="First Name"
          />
          {errors.address?.firstName && (
            <p className="text-sm text-red-600 mt-1">
              {errors.address.firstName.message}
            </p>
          )}
        </div>
        <div>
          <label className="block mb-1 font-medium">Last Name</label>
          <Input
            {...register('address.lastName', {
              required: 'Last name is required',
            })}
            placeholder="Last Name"
          />
          {errors.address?.lastName && (
            <p className="text-sm text-red-600 mt-1">
              {errors.address.lastName.message}
            </p>
          )}
        </div>
        <div className="md:col-span-2">
          <label className="block mb-1 font-medium">Address Line 1</label>
          <Input
            {...register('address.line1', {
              required: 'Address line 1 is required',
            })}
            placeholder="Address Line 1"
          />
          {errors.address?.line1 && (
            <p className="text-sm text-red-600 mt-1">
              {errors.address.line1.message}
            </p>
          )}
        </div>
        <div className="md:col-span-2">
          <label className="block mb-1 font-medium">Address Line 2</label>
          <Input
            {...register('address.line2')}
            placeholder="Address Line 2 (optional)"
          />
        </div>
        <div>
          <label className="block mb-1 font-medium">City</label>
          <Input
            {...register('address.city', { required: 'City is required' })}
            placeholder="City"
          />
          {errors.address?.city && (
            <p className="text-sm text-red-600 mt-1">
              {errors.address.city.message}
            </p>
          )}
        </div>
        <div>
          <label className="block mb-1 font-medium">State</label>
          <Input
            {...register('address.state', { required: 'State is required' })}
            placeholder="State"
          />
          {errors.address?.state && (
            <p className="text-sm text-red-600 mt-1">
              {errors.address.state.message}
            </p>
          )}
        </div>
        <div>
          <label className="block mb-1 font-medium">Postal Code</label>
          <Input
            {...register('address.postalCode', {
              required: 'Postal code is required',
            })}
            placeholder="Postal Code"
          />
          {errors.address?.postalCode && (
            <p className="text-sm text-red-600 mt-1">
              {errors.address.postalCode.message}
            </p>
          )}
        </div>
        <div>
          <label className="block mb-1 font-medium">Country</label>
          <Input
            {...register('address.country', {
              required: 'Country is required',
            })}
            placeholder="Country"
          />
          {errors.address?.country && (
            <p className="text-sm text-red-600 mt-1">
              {errors.address.country.message}
            </p>
          )}
        </div>
      </div>

      {/* Line Items */}
      <div className="space-y-4">
        <h3 className="font-semibold">Items</h3>
        {fields.map((field, index) => (
          <div
            key={field.id}
            className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end"
          >
            <div className="md:col-span-2">
              <label className="block mb-1 font-medium">Product</label>
              <select
                {...register(`items.${index}.inventoryItemId`, {
                  required: 'Product is required',
                })}
                disabled={loadingInventory}
                className="block w-full rounded-md border px-3 py-2"
              >
                <option value="">Select product</option>
                {inventory.map((item) => (
                  <option key={item.id} value={item.id}>
                    {item.sku} — Available: {item.available}
                  </option>
                ))}
              </select>
              {errors.items?.[index]?.inventoryItemId && (
                <p className="text-sm text-red-600 mt-1">
                  {errors.items[index]?.inventoryItemId?.message}
                </p>
              )}
              {inventoryError && (
                <p className="text-sm text-red-600">{inventoryError}</p>
              )}
            </div>
            <div>
              <label className="block mb-1 font-medium">Quantity</label>
              <Input
                type="number"
                {...register(`items.${index}.quantity`, {
                  required: 'Quantity is required',
                  valueAsNumber: true,
                  min: { value: 1, message: 'Quantity must be at least 1' },
                })}
                placeholder="Qty"
              />
              {errors.items?.[index]?.quantity && (
                <p className="text-sm text-red-600 mt-1">
                  {errors.items[index]?.quantity?.message}
                </p>
              )}
            </div>
            <div>
              <Button
                variant="destructive"
                type="button"
                onClick={() => remove(index)}
              >
                Remove
              </Button>
            </div>
          </div>
        ))}
        <Button
          type="button"
          onClick={() => append({ inventoryItemId: '', quantity: 1 })}
        >
          Add Item
        </Button>
      </div>

      {/* Submission */}
      {updateError && (
        <p className="text-sm text-red-600">{updateError}</p>
      )}
      <Button type="submit" disabled={updatingOrder}>
        {updatingOrder ? 'Updating Order...' : 'Update Order'}
      </Button>
    </form>
  )
}