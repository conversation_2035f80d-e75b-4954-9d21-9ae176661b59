'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Sparkles, 
  Layout, 
  FileCode, 
  Layers, 
  Globe,
  Wand2,
  ArrowRight,
  Zap,
  Palette,
  Code,
  Database
} from 'lucide-react'
import { 
  AIVisualEditorLayout, 
  NextJSLayoutGenerator, 
  ComponentLibrary 
} from '@/lib/ai-visual-editor'

export default function AIBuildersPage() {
  const [activeBuilder, setActiveBuilder] = useState<string | null>(null)

  const builders = [
    {
      id: 'visual-editor',
      title: 'AI Visual Editor',
      description: 'Generate React components with AI assistance and dynamic properties',
      icon: Sparkles,
      color: 'bg-blue-500',
      features: ['Component Generation', 'Properties Panel', 'Live Preview', 'Export Code'],
      status: 'ready'
    },
    {
      id: 'nextjs-generator',
      title: 'Next.js Generator',
      description: 'Create complete Next.js layouts and pages with AI',
      icon: Globe,
      color: 'bg-green-500',
      features: ['Layout Generation', 'Page Creation', 'Route Handling', 'SEO Optimization'],
      status: 'ready'
    },
    {
      id: 'component-library',
      title: 'Component Library',
      description: 'Browse and use pre-built component templates',
      icon: Layers,
      color: 'bg-purple-500',
      features: ['Template Gallery', 'Search & Filter', 'Usage Analytics', 'Community Sharing'],
      status: 'ready'
    },
    {
      id: 'layout-builder',
      title: 'AI Layout Builder',
      description: 'Design complex layouts with AI assistance',
      icon: Layout,
      color: 'bg-orange-500',
      features: ['Grid Systems', 'Responsive Design', 'Component Placement', 'Export Options'],
      status: 'beta'
    },
    {
      id: 'page-builder',
      title: 'AI Page Builder',
      description: 'Build complete pages with drag-and-drop and AI',
      icon: FileCode,
      color: 'bg-indigo-500',
      features: ['Drag & Drop', 'AI Suggestions', 'Content Management', 'SEO Tools'],
      status: 'beta'
    },
    {
      id: 'theme-generator',
      title: 'AI Theme Generator',
      description: 'Generate complete design systems and themes',
      icon: Palette,
      color: 'bg-pink-500',
      features: ['Color Palettes', 'Typography', 'Component Variants', 'Design Tokens'],
      status: 'coming-soon'
    }
  ]

  const renderBuilderInterface = () => {
    switch (activeBuilder) {
      case 'visual-editor':
        return <AIVisualEditorLayout />
      case 'nextjs-generator':
        return <NextJSLayoutGenerator />
      case 'component-library':
        return <ComponentLibrary />
      default:
        return null
    }
  }

  if (activeBuilder) {
    return (
      <div className="h-screen flex flex-col">
        <div className="h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setActiveBuilder(null)}
            >
              ← Back to Builders
            </Button>
            <div className="flex items-center space-x-2">
              {(() => {
                const builder = builders.find(b => b.id === activeBuilder)
                const Icon = builder?.icon
                return Icon ? <Icon className="w-5 h-5" /> : null
              })()}
              <h1 className="text-lg font-semibold">
                {builders.find(b => b.id === activeBuilder)?.title}
              </h1>
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-hidden">
          {renderBuilderInterface()}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <Wand2 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">AI Builders Suite</h1>
                <p className="text-gray-600">
                  Powerful AI-driven tools for building modern web applications
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>3 Ready</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span>2 Beta</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span>1 Coming Soon</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All Builders</TabsTrigger>
            <TabsTrigger value="ready">Ready</TabsTrigger>
            <TabsTrigger value="beta">Beta</TabsTrigger>
            <TabsTrigger value="coming-soon">Coming Soon</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {builders.map((builder) => (
                <BuilderCard
                  key={builder.id}
                  builder={builder}
                  onSelect={() => setActiveBuilder(builder.id)}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="ready" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {builders.filter(b => b.status === 'ready').map((builder) => (
                <BuilderCard
                  key={builder.id}
                  builder={builder}
                  onSelect={() => setActiveBuilder(builder.id)}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="beta" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {builders.filter(b => b.status === 'beta').map((builder) => (
                <BuilderCard
                  key={builder.id}
                  builder={builder}
                  onSelect={() => setActiveBuilder(builder.id)}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="coming-soon" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {builders.filter(b => b.status === 'coming-soon').map((builder) => (
                <BuilderCard
                  key={builder.id}
                  builder={builder}
                  onSelect={() => {}}
                />
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Quick Start Section */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Start</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Zap className="w-5 h-5 text-blue-600" />
                </div>
                <h3 className="font-semibold">Generate Components</h3>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Start with the AI Visual Editor to generate React components from natural language descriptions.
              </p>
              <Button 
                size="sm" 
                onClick={() => setActiveBuilder('visual-editor')}
                className="w-full"
              >
                Start Building
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Globe className="w-5 h-5 text-green-600" />
                </div>
                <h3 className="font-semibold">Create Pages</h3>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Use the Next.js Generator to create complete layouts and pages with proper routing.
              </p>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => setActiveBuilder('nextjs-generator')}
                className="w-full"
              >
                Generate Pages
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Layers className="w-5 h-5 text-purple-600" />
                </div>
                <h3 className="font-semibold">Browse Templates</h3>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Explore the component library for pre-built templates and community contributions.
              </p>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => setActiveBuilder('component-library')}
                className="w-full"
              >
                Browse Library
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

function BuilderCard({ 
  builder, 
  onSelect 
}: { 
  builder: any
  onSelect: () => void 
}) {
  const Icon = builder.icon
  const isDisabled = builder.status === 'coming-soon'

  return (
    <Card className={`p-6 transition-all hover:shadow-lg ${isDisabled ? 'opacity-60' : 'cursor-pointer'}`}>
      <div className="flex items-start justify-between mb-4">
        <div className={`p-3 ${builder.color} rounded-lg`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
        <Badge 
          variant={builder.status === 'ready' ? 'default' : builder.status === 'beta' ? 'secondary' : 'outline'}
          className="text-xs"
        >
          {builder.status === 'ready' ? 'Ready' : builder.status === 'beta' ? 'Beta' : 'Coming Soon'}
        </Badge>
      </div>

      <h3 className="text-lg font-semibold text-gray-900 mb-2">{builder.title}</h3>
      <p className="text-sm text-gray-600 mb-4">{builder.description}</p>

      <div className="space-y-2 mb-4">
        {builder.features.map((feature: string, index: number) => (
          <div key={index} className="flex items-center space-x-2 text-xs text-gray-500">
            <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            <span>{feature}</span>
          </div>
        ))}
      </div>

      <Button 
        onClick={onSelect}
        disabled={isDisabled}
        className="w-full"
        variant={isDisabled ? 'outline' : 'default'}
      >
        {isDisabled ? 'Coming Soon' : 'Open Builder'}
        {!isDisabled && <ArrowRight className="w-4 h-4 ml-2" />}
      </Button>
    </Card>
  )
}
