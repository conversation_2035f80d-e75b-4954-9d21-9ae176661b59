'use client'

import React, { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import {
  useInventoryItem,
  useInventoryMutations
} from '@/lib/ecommerce/hooks/use-inventory'
import type { StockMovement } from '@/lib/ecommerce/services/inventory-service'
import type { InventoryItem } from '@/lib/ecommerce/types/inventory'

export default function InventoryItemDetailPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const productId = params.id as string
  const variantId = searchParams.get('variantId') || undefined

  const {
    inventoryItem,
    loading: itemLoading,
    error: itemError,
    refetch: refetchItem,
    clearError: clearItemError
  } = useInventoryItem({ productId, variantId })

  const {
    adjustInventory,
    updateInventory,
    reserveInventory,
    releaseReservation,
    loading: mutLoading,
    error: mutError,
    clearError: clearMutError
  } = useInventoryMutations()

  const [adjustValue, setAdjustValue] = useState<string>('')
  const [adjustReason, setAdjustReason] = useState<string>('')
  const [updateQty, setUpdateQty] = useState<string>('')
  const [updateCost, setUpdateCost] = useState<string>('')
  const [reserveQty, setReserveQty] = useState<string>('')
  const [reserveOrder, setReserveOrder] = useState<string>('')
  const [releaseQty, setReleaseQty] = useState<string>('')
  const [releaseOrder, setReleaseOrder] = useState<string>('')

  const [history, setHistory] = useState<StockMovement[]>([])
  const [histLoading, setHistLoading] = useState<boolean>(false)
  const [histError, setHistError] = useState<string | null>(null)

  useEffect(() => {
    if (!productId) return
    setHistLoading(true)
    setHistError(null)
    const q = new URLSearchParams()
    q.append('productId', productId)
    if (variantId) q.append('variantId', variantId)
    fetch(`/api/e-commerce/inventory/movements?${q.toString()}`, {
      headers: { 'x-admin-request': 'true' }
    })
      .then(res => res.json())
      .then((res) => {
        if (Array.isArray(res)) {
          setHistory(res)
        } else if (res.success && Array.isArray(res.data)) {
          setHistory(res.data)
        } else {
          setHistError('Failed to load history')
        }
      })
      .catch(() => setHistError('Failed to load history'))
      .finally(() => setHistLoading(false))
  }, [productId, variantId])

  const handleAdjust = async (e: React.FormEvent) => {
    e.preventDefault()
    clearMutError()
    const ok = await adjustInventory(
      productId,
      variantId,
      Number(adjustValue),
      adjustReason
    )
    if (ok) {
      setAdjustValue('')
      setAdjustReason('')
      await refetchItem()
    }
  }

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    clearMutError()
    const ok = await updateInventory(
      productId,
      variantId,
      Number(updateQty),
      updateCost ? Number(updateCost) : undefined
    )
    if (ok) {
      setUpdateQty('')
      setUpdateCost('')
      await refetchItem()
    }
  }

  const handleReserve = async (e: React.FormEvent) => {
    e.preventDefault()
    clearMutError()
    const ok = await reserveInventory(
      productId,
      variantId,
      Number(reserveQty),
      reserveOrder
    )
    if (ok) {
      setReserveQty('')
      setReserveOrder('')
      await refetchItem()
    }
  }

  const handleRelease = async (e: React.FormEvent) => {
    e.preventDefault()
    clearMutError()
    const ok = await releaseReservation(
      productId,
      variantId,
      Number(releaseQty),
      releaseOrder
    )
    if (ok) {
      setReleaseQty('')
      setReleaseOrder('')
      await refetchItem()
    }
  }

  if (itemLoading) {
    return <p>Loading inventory item...</p>
  }

  if (itemError) {
    return (
      <Card>
        <CardContent>
          <p className="text-destructive">{itemError}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/admin/inventory" className="hover:underline">Inventory</Link>
        <span>/</span>
        <span>Item {inventoryItem?.id || productId}</span>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Stock Overview</CardTitle>
        </CardHeader>
        <CardContent>
          {inventoryItem ? (
            <div className="grid grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Quantity</p>
                <p className="text-lg font-medium">{inventoryItem.quantity}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Reserved</p>
                <p className="text-lg font-medium">{inventoryItem.reservedQuantity}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Available</p>
                <p className="text-lg font-medium">{inventoryItem.availableQuantity}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Committed</p>
                <p className="text-lg font-medium">{inventoryItem.committedQuantity}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Low Stock Threshold</p>
                <p className="text-lg font-medium">{inventoryItem.lowStockThreshold}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Out of Stock Threshold</p>
                <p className="text-lg font-medium">{inventoryItem.outOfStockThreshold}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Reorder Point</p>
                <p className="text-lg font-medium">{inventoryItem.reorderPoint}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Cost Price</p>
                <p className="text-lg font-medium">
                  {inventoryItem.costPrice.currency} {inventoryItem.costPrice.amount.toFixed(2)}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Average Cost</p>
                <p className="text-lg font-medium">
                  {inventoryItem.averageCost.currency} {inventoryItem.averageCost.amount.toFixed(2)}
                </p>
              </div>
              {inventoryItem.attributes && Object.keys(inventoryItem.attributes).length > 0 && (
                <div className="col-span-3">
                  <p className="text-sm text-muted-foreground">Attributes</p>
                  <div className="text-sm">
                    {Object.entries(inventoryItem.attributes).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="font-medium">{key}:</span>
                        <span>{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {inventoryItem.tags && inventoryItem.tags.length > 0 && (
                <div className="col-span-3">
                  <p className="text-sm text-muted-foreground">Tags</p>
                  <div className="flex flex-wrap gap-1">
                    {inventoryItem.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-secondary text-secondary-foreground rounded-sm text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p>No inventory data.</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Adjust Inventory</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleAdjust} className="space-y-4">
            <div>
              <Label htmlFor="adjustValue">Adjustment</Label>
              <Input
                id="adjustValue"
                type="number"
                value={adjustValue}
                onChange={e => setAdjustValue(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="adjustReason">Reason</Label>
              <Textarea
                id="adjustReason"
                value={adjustReason}
                onChange={e => setAdjustReason(e.target.value)}
                required
              />
            </div>
            {mutError && <p className="text-destructive">{mutError}</p>}
            <Button type="submit" disabled={mutLoading}>
              {mutLoading ? 'Processing...' : 'Apply Adjustment'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Update Inventory</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleUpdate} className="space-y-4">
            <div>
              <Label htmlFor="updateQty">Quantity</Label>
              <Input
                id="updateQty"
                type="number"
                value={updateQty}
                onChange={e => setUpdateQty(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="updateCost">Cost (optional)</Label>
              <Input
                id="updateCost"
                type="number"
                step="0.01"
                value={updateCost}
                onChange={e => setUpdateCost(e.target.value)}
              />
            </div>
            {mutError && <p className="text-destructive">{mutError}</p>}
            <Button type="submit" disabled={mutLoading}>
              {mutLoading ? 'Updating...' : 'Update Inventory'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Reserve Inventory</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleReserve} className="space-y-4">
            <div>
              <Label htmlFor="reserveQty">Quantity</Label>
              <Input
                id="reserveQty"
                type="number"
                value={reserveQty}
                onChange={e => setReserveQty(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="reserveOrder">Order ID</Label>
              <Input
                id="reserveOrder"
                value={reserveOrder}
                onChange={e => setReserveOrder(e.target.value)}
                required
              />
            </div>
            {mutError && <p className="text-destructive">{mutError}</p>}
            <Button type="submit" disabled={mutLoading}>
              {mutLoading ? 'Reserving...' : 'Reserve'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Release Reservation</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleRelease} className="space-y-4">
            <div>
              <Label htmlFor="releaseQty">Quantity</Label>
              <Input
                id="releaseQty"
                type="number"
                value={releaseQty}
                onChange={e => setReleaseQty(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="releaseOrder">Order ID</Label>
              <Input
                id="releaseOrder"
                value={releaseOrder}
                onChange={e => setReleaseOrder(e.target.value)}
                required
              />
            </div>
            {mutError && <p className="text-destructive">{mutError}</p>}
            <Button type="submit" disabled={mutLoading}>
              {mutLoading ? 'Releasing...' : 'Release'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Stock History</CardTitle>
        </CardHeader>
        <CardContent>
          {histLoading && <p>Loading history...</p>}
          {histError && <p className="text-destructive">{histError}</p>}
          {!histLoading && !histError && (
            history.length > 0 ? (
              <div className="overflow-auto">
                <table className="w-full text-left">
                  <thead>
                    <tr>
                      <th className="py-2">Date</th>
                      <th className="py-2">Type</th>
                      <th className="py-2">Qty</th>
                      <th className="py-2">Reason</th>
                      <th className="py-2">Reference</th>
                    </tr>
                  </thead>
                  <tbody>
                    {history.map(h => (
                      <tr key={h.id} className="border-t">
                        <td className="py-1">{new Date(h.createdAt).toLocaleString()}</td>
                        <td className="py-1">{h.type}</td>
                        <td className="py-1">{h.quantity}</td>
                        <td className="py-1">{h.reason}</td>
                        <td className="py-1">{h.referenceNumber || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p>No history available.</p>
            )
          )}
        </CardContent>
      </Card>
    </div>
  )
}
