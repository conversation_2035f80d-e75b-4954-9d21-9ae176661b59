'use client'

import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { 
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { useInventory } from '@/lib/ecommerce/hooks/use-inventory'
import { EnhancedInventoryList } from '@/components/admin/inventory/enhanced-inventory-list'
import {
  Package,
  Plus,
  Home
} from 'lucide-react'

export default function InventoryPage() {
  const router = useRouter()
  const { inventory, loading, pagination, searchInventory } = useInventory()

  const handleCreateItem = () => {
    router.push('/admin/products/new')
  }

  const handleEditItem = (item: any) => {
    router.push(`/admin/products/${item.productId}/edit`)
  }

  const handleViewItem = (item: any) => {
    const inventoryId = item.variantId ? `${item.productId}/${item.variantId}` : item.productId
    router.push(`/admin/inventory/${inventoryId}`)
  }

  const handleAdjustStock = (item: any) => {
    // TODO: Open stock adjustment modal
    console.log('Adjust stock for:', item.productTitle)
  }

  const handleRowClick = ({ productId, variantId }: { productId: string; variantId?: string }) => {
    const inventoryId = variantId ? `${productId}/${variantId}` : productId
    router.push(`/admin/inventory/${inventoryId}`)
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">
              <Home className="h-4 w-4" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Inventory</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Enhanced Inventory List */}
      <EnhancedInventoryList
        onCreateItem={handleCreateItem}
        onEditItem={handleEditItem}
        onViewItem={handleViewItem}
        onAdjustStock={handleAdjustStock}
      />
    </div>
  )
}
