"use client"

import { useEffect } from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { MapPin, 
  Phone, 
  Clock, 
  Navigation, 
  Search,
  Store,
  Calendar,
  Users, Loader2 } from "lucide-react"

const stores = [
  {
    id: 1,
    name: "Coco Milk Kids - Downtown",
    address: "123 Main Street, Downtown, NY 10001",
    phone: "(*************",
    hours: {
      weekdays: "10:00 AM - 8:00 PM",
      saturday: "10:00 AM - 9:00 PM",
      sunday: "11:00 AM - 6:00 PM"
    },
    services: ["Personal Styling", "Gift Wrapping", "Size Consultation"],
    distance: "0.5 miles",
    isOpen: true
  },
  {
    id: 2,
    name: "Coco Milk Kids - Mall Plaza",
    address: "456 Shopping Center Dr, Mall Plaza, NY 10002",
    phone: "(*************",
    hours: {
      weekdays: "10:00 AM - 9:00 PM",
      saturday: "10:00 AM - 10:00 PM",
      sunday: "11:00 AM - 7:00 PM"
    },
    services: ["Personal Styling", "Gift Wrapping", "Birthday Parties"],
    distance: "2.3 miles",
    isOpen: true
  },
  {
    id: 3,
    name: "Coco Milk Kids - Westside",
    address: "789 West Avenue, Westside, NY 10003",
    phone: "(*************",
    hours: {
      weekdays: "10:00 AM - 8:00 PM",
      saturday: "10:00 AM - 9:00 PM",
      sunday: "12:00 PM - 6:00 PM"
    },
    services: ["Personal Styling", "Size Consultation", "Special Orders"],
    distance: "4.1 miles",
    isOpen: false
  },
  {
    id: 4,
    name: "Coco Milk Kids - Northgate",
    address: "321 North Gate Blvd, Northgate, NY 10004",
    phone: "(*************",
    hours: {
      weekdays: "9:00 AM - 8:00 PM",
      saturday: "9:00 AM - 9:00 PM",
      sunday: "11:00 AM - 6:00 PM"
    },
    services: ["Personal Styling", "Gift Wrapping", "Size Consultation", "Birthday Parties"],
    distance: "6.8 miles",
    isOpen: true
  }
]

export default function StoresPage() {
        const [searchLocation, setSearchLocation] = useState("")
  const [filteredStores, setFilteredStores] = useState(stores)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, this would search by location
    console.log("Searching for stores near:", searchLocation)
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold font-montserrat mb-4">Find a Store</h1>
          <p className="text-muted-foreground text-lg">
            Visit one of our locations for personalized service and the full Coco Milk Kids experience
          </p>
        </div>

        {/* Search */}
        <div className="max-w-md mx-auto mb-8">
          <form onSubmit={handleSearch} className="relative">
            <Input
              type="text"
              placeholder="Enter your city, state, or ZIP code"
              value={searchLocation}
              onChange={(e) => setSearchLocation(e.target.value)}
              className="pr-10"
            />
            <Button type="submit" size="icon" className="absolute right-0 top-0 h-10 w-10">
              <Search className="h-4 w-4" />
            </Button>
          </form>
        </div>

        {/* Store List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredStores.map((store) => (
            <Card key={store.id} className="h-full">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center space-x-2">
                      <Store className="h-5 w-5" />
                      <span>{store.name}</span>
                    </CardTitle>
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge variant={store.isOpen ? "default" : "secondary"}>
                        {store.isOpen ? "Open" : "Closed"}
                      </Badge>
                      <span className="text-sm text-muted-foreground">{store.distance}</span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Address */}
                <div className="flex items-start space-x-3">
                  <MapPin className="h-4 w-4 mt-1 text-muted-foreground" />
                  <div>
                    <p className="text-sm">{store.address}</p>
                    <Button variant="link" className="p-0 h-auto text-primary text-sm">
                      <Navigation className="h-3 w-3 mr-1" />
                      Get Directions
                    </Button>
                  </div>
                </div>

                {/* Phone */}
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <a href={`tel:${store.phone}`} className="text-sm hover:underline">
                    {store.phone}
                  </a>
                </div>

                {/* Hours */}
                <div className="flex items-start space-x-3">
                  <Clock className="h-4 w-4 mt-1 text-muted-foreground" />
                  <div className="text-sm">
                    <div>Mon-Fri: {store.hours.weekdays}</div>
                    <div>Saturday: {store.hours.saturday}</div>
                    <div>Sunday: {store.hours.sunday}</div>
                  </div>
                </div>

                <Separator />

                {/* Services */}
                <div>
                  <h4 className="font-medium mb-2 flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Services Available
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {store.services.map((service) => (
                      <Badge key={service} variant="outline" className="text-xs">
                        {service}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Button variant="outline" className="flex-1">
                    <Calendar className="h-4 w-4 mr-2" />
                    Book Appointment
                  </Button>
                  <Button className="flex-1">
                    <Phone className="h-4 w-4 mr-2" />
                    Call Store
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 mx-auto mb-3 text-primary" />
              <h3 className="font-medium mb-2">Personal Styling</h3>
              <p className="text-sm text-muted-foreground">
                Get expert advice from our trained stylists to find the perfect outfits for your child.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Calendar className="h-8 w-8 mx-auto mb-3 text-primary" />
              <h3 className="font-medium mb-2">Birthday Parties</h3>
              <p className="text-sm text-muted-foreground">
                Host your child's birthday party at select locations with special activities and discounts.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Store className="h-8 w-8 mx-auto mb-3 text-primary" />
              <h3 className="font-medium mb-2">Exclusive In-Store Items</h3>
              <p className="text-sm text-muted-foreground">
                Discover exclusive collections and limited edition items only available in our stores.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
