"use client"

import dynamic from "next/dynamic"
import { Loader2 } from "lucide-react"

const ProductCardShowcase = dynamic(() => import("@/components/storefront/products/product-card-showcase").then(mod => ({ default: mod.ProductCardShowcase })), {
  loading: () => <div className="flex items-center justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>,
  ssr: false
})

export default function ProductCardShowcasePage() {
  return <ProductCardShowcase />
}

