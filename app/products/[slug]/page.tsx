"use client"

import { notFound } from "next/navigation"
import { useEffect, useState } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, RefreshCw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

import { ProductInfo } from "@/components/storefront/products/product-info"
import { RelatedProducts } from "@/components/related-products"
import { SizeRecommendation } from "@/components/size-recommendation"
import { AIProductRecommendations } from "@/components/storefront/products/ai-product-recommendations"
import { useProduct } from "@/lib/ecommerce/hooks/use-products"
import { transformToStorefrontProduct } from "@/lib/ecommerce/utils/product-transformers"
import type { Product } from "@/lib/ecommerce/types"

interface Props {
  params: {
    slug: string
  }
}

function ProductPageContent({ slug }: { slug: string }) {
  const { product: ecommerceProduct, loading, error, refetch } = useProduct({ slug, autoFetch: true })
  const [retryCount, setRetryCount] = useState(0)

  // Transform ecommerce product to storefront format
  const product = ecommerceProduct ? transformToStorefrontProduct(ecommerceProduct) : null

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    refetch()
  }

  // Loading state
  if (loading) {
    return (
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
          {/* Product Gallery Skeleton */}
          <div className="space-y-4">
            <Skeleton className="aspect-square w-full rounded-lg" />
            <div className="grid grid-cols-4 gap-2">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="aspect-square w-full rounded-lg" />
              ))}
            </div>
          </div>

          {/* Product Info Skeleton */}
          <div className="space-y-6">
            <div className="space-y-3">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-6 w-1/2" />
            </div>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="space-y-3">
              <Skeleton className="h-6 w-1/4" />
              <div className="flex gap-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Skeleton key={i} className="h-8 w-8 rounded-full" />
                ))}
              </div>
            </div>
            <div className="space-y-3">
              <Skeleton className="h-6 w-1/4" />
              <div className="flex gap-2">
                {Array.from({ length: 4 }).map((_, i) => (
                  <Skeleton key={i} className="h-10 w-10" />
                ))}
              </div>
            </div>
            <div className="flex gap-4">
              <Skeleton className="h-12 flex-1" />
              <Skeleton className="h-12 flex-1" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load product: {error.message}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Product not found
  if (!product) {
    notFound()
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
        {/* Product Gallery */}
        <div className="space-y-4">
          <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={product.images?.[0] || "https://images.unsplash.com/photo-1519238359922-989348752efb?q=80&w=800&h=800&auto=format&fit=crop"}
              alt={product.name}
              className="w-full h-full object-cover"
              loading="eager"
            />
          </div>
          {product.images && product.images.length > 1 && (
            <div className="grid grid-cols-4 gap-2">
              {product.images.slice(1, 5).map((image, index) => (
                <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={image}
                    alt={`${product.name} ${index + 2}`}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div>
          <ProductInfo product={product} />
        </div>
      </div>

      {/* Related Products */}
      <div className="mt-16">
        <RelatedProducts productId={product.id} />
      </div>

      {/* Size Recommendation */}
      <div className="mt-8">
        <SizeRecommendation category={product.categoryId || ''} />
      </div>

      {/* AI Recommendations */}
      <div className="mt-16">
        <AIProductRecommendations productId={product.id} />
      </div>
    </div>
  )
}

export default function ProductPage({ params }: Props) {
  return <ProductPageContent slug={params.slug} />
}
