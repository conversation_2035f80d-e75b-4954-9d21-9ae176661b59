"use client"

import { SmartSearch } from "@/components/smart-search"
import { ProductGrid } from "@/components/storefront/products/product-grid"
import { ProductFilters } from "@/components/storefront/products/product-filters"
import { ProductSort } from "@/components/storefront/products/product-sort"
import { useSearchParams } from "next/navigation"
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"

function SearchContent() {
  const searchParams = useSearchParams()
  const query = searchParams.get('q') || ''
  const sort = searchParams.get('sort') || ''
  const category = searchParams.get('category') || ''
  const color = searchParams.get('color') || ''
  const size = searchParams.get('size') || ''

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-2">
          {query ? `Search Results for "${query}"` : 'Search Products'}
        </h1>
        {query && (
          <p className="text-muted-foreground">
            Showing results for your search query
          </p>
        )}
      </div>

      <div className="mb-8">
        <SmartSearch />
      </div>

      {query && (
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="sticky top-4">
              <ProductFilters />
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Sort and Results Count */}
            <div className="flex justify-between items-center mb-6">
              <div className="text-sm text-muted-foreground">
                Search results
              </div>
              <ProductSort />
            </div>

            {/* Product Grid with Search Query */}
            <Suspense fallback={
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
                {Array.from({ length: 12 }).map((_, i) => (
                  <div key={i} className="space-y-3">
                    <Skeleton className="aspect-[3/4] w-full" />
                    <Skeleton className="h-4 w-2/3" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                ))}
              </div>
            }>
              <ProductGrid
                sort={sort}
                category={category}
                color={color}
                size={size}
                searchQuery={query}
                showFiltersApplied={true}
              />
            </Suspense>
          </div>
        </div>
      )}

      {!query && (
        <div className="text-center py-12">
          <h2 className="text-xl font-medium mb-2">Start your search</h2>
          <p className="text-muted-foreground">
            Enter a search term above to find products
          </p>
        </div>
      )}
    </div>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <Skeleton className="h-8 w-64 mb-6" />
        <Skeleton className="h-12 w-full mb-8" />
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="aspect-[3/4] w-full" />
              <Skeleton className="h-4 w-2/3" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  )
}
