#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// List of files that were modified and need cleanup
const filesToCleanup = [
  'app/account/dashboard/page.tsx',
  'app/collections/new-arrivals/page.tsx',
  'app/collections/sale/page.tsx',
  'app/collections/school-uniforms/page.tsx',
  'app/collections/summer/page.tsx',
  'app/stores/page.tsx',
  'app/page.tsx',
  'app/terms/page.tsx',
  'app/cart/page.tsx',
  'app/wishlist/page.tsx',
  'app/checkout/page.tsx',
  'app/collections/heritage-day/page.tsx',
  'app/collections/best-sellers/page.tsx',
  'app/product-card-showcase/page.tsx'
];

// Content files to remove
const contentFilesToRemove = [
  'app/account/dashboard/dashboardpage-content.tsx',
  'app/collections/new-arrivals/newarrivalspage-content.tsx',
  'app/collections/sale/salepage-content.tsx',
  'app/collections/school-uniforms/schooluniformspage-content.tsx',
  'app/collections/summer/summerpage-content.tsx',
  'app/stores/storespage-content.tsx',
  'app/apppage-content.tsx',
  'app/terms/terms-content.tsx'
];

function cleanupDynamicImports(filePath) {
  try {
    console.log(`Cleaning up: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    
    // Skip if it doesn't have dynamic imports
    if (!content.includes('dynamic(') || !content.includes('ssr: false')) {
      console.log(`No dynamic imports found in: ${filePath}`);
      return;
    }

    // Find the corresponding content file
    const dir = path.dirname(filePath);
    const contentFiles = fs.readdirSync(dir).filter(file => 
      file.endsWith('-content.tsx') || file.endsWith('page-content.tsx')
    );

    if (contentFiles.length > 0) {
      const contentFile = path.join(dir, contentFiles[0]);
      if (fs.existsSync(contentFile)) {
        console.log(`Found content file: ${contentFile}`);
        
        // Read the content file and restore it to the main file
        const contentFileContent = fs.readFileSync(contentFile, 'utf8');
        
        // Write the content back to the main file
        fs.writeFileSync(filePath, contentFileContent, 'utf8');
        
        // Remove the content file
        fs.unlinkSync(contentFile);
        
        console.log(`✅ Restored: ${filePath} and removed ${contentFile}`);
      }
    } else {
      console.log(`No content file found for: ${filePath}`);
    }

  } catch (error) {
    console.error(`❌ Error cleaning up ${filePath}:`, error.message);
  }
}

function removeSSRProtectionCode(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    
    // Remove SSR protection code patterns
    content = content.replace(/const \[isClient, setIsClient\] = useState\(false\)\s*\n/, '');
    content = content.replace(/\/\/ Prevent SSR issues by only rendering after hydration\s*\n\s*useEffect\(\(\) => \{\s*\n\s*setIsClient\(true\)\s*\n\s*\}, \[\]\)\s*\n/, '');
    content = content.replace(/\/\/ Show loading state during SSR\/hydration\s*\n\s*if \(!isClient\) \{[\s\S]*?\}\s*\n/, '');
    
    // Remove unnecessary imports if they're not used elsewhere
    if (!content.includes('useState') && !content.includes('setIsClient')) {
      content = content.replace(/import \{ useState, useEffect \} from "react"\s*\n/, '');
      content = content.replace(/import \{ useEffect \} from "react"\s*\n/, '');
      content = content.replace(/import \{ useState \} from "react"\s*\n/, '');
    }
    
    if (!content.includes('Loader2')) {
      content = content.replace(/, Loader2/g, '');
      content = content.replace(/Loader2, /g, '');
      content = content.replace(/import \{ Loader2 \} from "lucide-react"\s*\n/, '');
    }

    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Cleaned SSR protection code from: ${filePath}`);

  } catch (error) {
    console.error(`❌ Error removing SSR protection from ${filePath}:`, error.message);
  }
}

function removeContentFiles() {
  contentFilesToRemove.forEach(filePath => {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`✅ Removed content file: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ Error removing ${filePath}:`, error.message);
    }
  });
}

// Main execution
console.log('🧹 Starting cleanup of individual SSR fixes...\n');

// Clean up dynamic imports first
filesToCleanup.forEach(cleanupDynamicImports);

// Remove any remaining SSR protection code
filesToCleanup.forEach(removeSSRProtectionCode);

// Remove any remaining content files
removeContentFiles();

console.log('\n✨ Cleanup completed!');
console.log('\n📝 Summary:');
console.log('  - Restored original page content from content files');
console.log('  - Removed dynamic import wrappers');
console.log('  - Removed individual SSR protection code');
console.log('  - Cleaned up content files');
console.log('\nSSR protection is now handled at the parent LayoutWrapper level.');
console.log('\nRun "npm run build" to test the parent-level SSR protection.');
