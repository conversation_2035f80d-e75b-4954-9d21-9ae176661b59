#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// List of pages that need SSR disabled
const pagesToFix = [
  'app/account/dashboard/page.tsx',
  'app/collections/new-arrivals/page.tsx',
  'app/collections/sale/page.tsx',
  'app/collections/school-uniforms/page.tsx',
  'app/collections/summer/page.tsx',
  'app/stores/page.tsx',
  'app/page.tsx'
];

// Template for Next.js dynamic import SSR fix
const getDynamicSSRTemplate = (componentName, loadingMessage) => `"use client"

import dynamic from "next/dynamic"
import { Loader2 } from "lucide-react"

// Use Next.js dynamic import to disable SSR
const DynamicComponent = dynamic(() => import('./${componentName.toLowerCase()}-content'), {
  loading: () => (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      <div className="flex items-center justify-center py-16">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">${loadingMessage}</span>
      </div>
    </div>
  ),
  ssr: false
})

export default function ${componentName}() {
  return <DynamicComponent />
}`;

function extractComponentName(filePath) {
  const fileName = path.basename(filePath, '.tsx');
  if (fileName === 'page') {
    const dirName = path.basename(path.dirname(filePath));
    return dirName.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join('') + 'Page';
  }
  return fileName.charAt(0).toUpperCase() + fileName.slice(1) + 'Page';
}

function getLoadingMessage(filePath) {
  const dirName = path.basename(path.dirname(filePath));
  if (dirName === 'account') return 'Loading dashboard...';
  if (dirName === 'collections') return 'Loading collection...';
  if (dirName === 'stores') return 'Loading stores...';
  if (filePath.includes('app/page.tsx')) return 'Loading page...';
  return 'Loading...';
}

function fixSSRIssues(filePath) {
  try {
    console.log(`Fixing SSR issues in: ${filePath}`);

    if (!fs.existsSync(filePath)) {
      console.log(`File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');

    // Skip if already uses dynamic import
    if (content.includes('dynamic(') && content.includes('ssr: false')) {
      console.log(`Already fixed with dynamic import: ${filePath}`);
      return;
    }

    const componentName = extractComponentName(filePath);
    const loadingMessage = getLoadingMessage(filePath);
    const contentFileName = componentName.toLowerCase() + '-content';

    // Create content file with original component
    const contentFilePath = path.join(path.dirname(filePath), contentFileName + '.tsx');

    // Extract the original component content (everything after imports)
    const lines = content.split('\n');
    let contentStartIndex = -1;
    let hasUseClient = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (line === '"use client"') {
        hasUseClient = true;
        continue;
      }

      if (line.startsWith('export default function')) {
        contentStartIndex = i;
        break;
      }
    }

    if (contentStartIndex === -1) {
      console.log(`Could not find component start in: ${filePath}`);
      return;
    }

    // Create content file with original component
    const originalContent = lines.slice(0, contentStartIndex).join('\n') + '\n' +
                           lines.slice(contentStartIndex).join('\n').replace('export default function', 'export default function');

    // Write content file
    fs.writeFileSync(contentFilePath, originalContent, 'utf8');

    // Create new main file with dynamic import
    const newMainContent = getDynamicSSRTemplate(componentName, loadingMessage);

    // Write the new main file
    fs.writeFileSync(filePath, newMainContent, 'utf8');
    console.log(`✅ Fixed: ${filePath} (created ${contentFileName}.tsx)`);

  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
  }
}

// Main execution
console.log('🔧 Starting SSR fixes...\n');

pagesToFix.forEach(fixSSRIssues);

console.log('\n✨ SSR fixes completed!');
console.log('\nRun "npm run build" to test the fixes.');
