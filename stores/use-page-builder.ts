import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { PageBlock, PageBuilderState, PageData } from '@/lib/page-builder/types'

// Type aliases for compatibility
type Block = PageBlock
type DevicePreview = 'desktop' | 'tablet' | 'mobile'

interface PageBuilderStore extends PageBuilderState {
  // Actions
  addBlock: (blockType: string, position?: number) => void
  updateBlock: (blockId: string, updates: Partial<PageBlock>) => void
  deleteBlock: (blockId: string) => void
  duplicateBlock: (blockId: string) => void
  moveBlock: (blockId: string, newPosition: number) => void
  selectBlock: (blockId: string | null) => void
  setDevicePreview: (device: DevicePreview) => void
  setPreviewMode: (enabled: boolean) => void
  undo: () => void
  redo: () => void
  saveState: () => void
  loadState: (state: Partial<PageBuilderState>) => void
  reset: () => void
  
  // New actions for better integration
  setHasUnsavedChanges: (hasChanges: boolean) => void
  setCanUndo: (canUndo: boolean) => void
  setCanRedo: (canRedo: boolean) => void
}

// Initial state
const initialState: PageBuilderState = {
  page: {
    id: '',
    title: '',
    slug: '',
    status: 'draft',
    type: 'custom',
    blocks: [],
    settings: {
      title: '',
      description: '',
      seoTitle: '',
      seoDescription: '',
      seoKeywords: [],
      customCss: '',
      customJs: ''
    }
  },
  selectedBlockId: null,
  devicePreview: 'desktop',
  isPreviewMode: false,
  hasUnsavedChanges: false,
  isDragging: false,
  isSaving: false,
  history: [],
  historyIndex: -1,
  canUndo: false,
  canRedo: false
}

export const usePageBuilder = create<PageBuilderStore>()(
  devtools((set, get) => ({
    ...initialState,

    // Add block
    addBlock: (blockType: string, position?: number) => {
      const state = get()
      const newBlock: Block = {
        id: `block-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: blockType,
        position: position ?? state.page.blocks.length,
        configuration: {},
        styling: {},
        isVisible: true,
        responsive: {
          hideOnMobile: false,
          hideOnTablet: false,
          hideOnDesktop: false
        }
      }
      
      const newBlocks = [...state.page.blocks]
      if (position !== undefined) {
        newBlocks.splice(position, 0, newBlock)
        // Update positions of subsequent blocks
        newBlocks.forEach((block, index) => {
          block.position = index
        })
      } else {
        newBlocks.push(newBlock)
      }
      
      set({
        page: {
          ...state.page,
          blocks: newBlocks
        },
        selectedBlockId: newBlock.id,
        hasUnsavedChanges: true
      })
    },

    // Update block
    updateBlock: (blockId: string, updates: Partial<Block>) => {
      const state = get()
      const blockIndex = state.page.blocks.findIndex(b => b.id === blockId)
      if (blockIndex !== -1) {
        const newBlocks = [...state.page.blocks]
        newBlocks[blockIndex] = { ...newBlocks[blockIndex], ...updates }
        set({
          page: {
            ...state.page,
            blocks: newBlocks
          },
          hasUnsavedChanges: true
        })
      }
    },

    // Delete block
    deleteBlock: (blockId: string) => {
      const state = get()
      const newBlocks = state.page.blocks
        .filter(b => b.id !== blockId)
        .map((block, index) => ({
          ...block,
          position: index
        }))
      
      set({
        page: {
          ...state.page,
          blocks: newBlocks
        },
        selectedBlockId: state.selectedBlockId === blockId ? null : state.selectedBlockId,
        hasUnsavedChanges: true
      })
    },

    // Duplicate block
    duplicateBlock: (blockId: string) => {
      const state = get()
      const originalBlock = state.page.blocks.find(b => b.id === blockId)
      if (originalBlock) {
        const newBlock: Block = {
          ...originalBlock,
          id: `block-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          position: originalBlock.position + 1
        }
        
        const newBlocks = [...state.page.blocks]
        newBlocks.splice(originalBlock.position + 1, 0, newBlock)
        newBlocks.forEach((block, index) => {
          block.position = index
        })
        
        set({
          page: {
            ...state.page,
            blocks: newBlocks
          },
          selectedBlockId: newBlock.id,
          hasUnsavedChanges: true
        })
      }
    },

    // Move block
    moveBlock: (blockId: string, newPosition: number) => {
      const state = get()
      const blockIndex = state.page.blocks.findIndex(b => b.id === blockId)
      if (blockIndex !== -1) {
        const newBlocks = [...state.page.blocks]
        const [block] = newBlocks.splice(blockIndex, 1)
        newBlocks.splice(newPosition, 0, block)
        newBlocks.forEach((block, index) => {
          block.position = index
        })
        
        set({
          page: {
            ...state.page,
            blocks: newBlocks
          },
          hasUnsavedChanges: true
        })
      }
    },

    // Select block
    selectBlock: (blockId: string | null) => {
      set({ selectedBlockId: blockId })
    },

    // Set device preview
    setDevicePreview: (device: DevicePreview) => {
      set({ devicePreview: device })
    },

    // Set preview mode
    setPreviewMode: (enabled: boolean) => {
      set({ isPreviewMode: enabled })
    },

    // Undo (placeholder)
    undo: () => {
      // TODO: Implement undo functionality
      console.log('Undo action')
    },

    // Redo (placeholder)
    redo: () => {
      // TODO: Implement redo functionality
      console.log('Redo action')
    },

    // Save state
    saveState: () => {
      set({ hasUnsavedChanges: false })
    },

    // Load state
    loadState: (newState: Partial<PageBuilderState>) => {
      set(state => ({ ...state, ...newState }))
    },

    // Reset
    reset: () => {
      set(initialState)
    },

    // Set unsaved changes
    setHasUnsavedChanges: (hasChanges: boolean) => {
      set({ hasUnsavedChanges: hasChanges })
    },

    // Set can undo
    setCanUndo: (canUndo: boolean) => {
      set({ canUndo: canUndo })
    },

    // Set can redo
    setCanRedo: (canRedo: boolean) => {
      set({ canRedo: canRedo })
    }
  }), {
    name: 'page-builder-store'
  })
)
