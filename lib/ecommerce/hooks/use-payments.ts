// React hooks for payment management
'use client'

import { useState, useCallback } from 'react'
import { ApiResponse } from '../types/base'

export interface PaymentMethod {
  id: string
  name: string
  type: 'payfast' | 'ozow' | 'card' | 'eft'
  enabled: boolean
  config: Record<string, any>
}

export interface PaymentRequest {
  orderId: string
  amount: number
  currency: 'ZAR'
  customerEmail: string
  customerName: string
  description: string
  returnUrl: string
  cancelUrl: string
  notifyUrl: string
}

export interface PaymentResponse {
  success: boolean
  paymentId: string
  redirectUrl?: string
  error?: string
}

export interface UsePaymentsReturn {
  paymentMethods: PaymentMethod[]
  loading: boolean
  error: { code: string; message: string } | null
  createPayment: (method: string, request: PaymentRequest) => Promise<PaymentResponse | null>
  verifyPayment: (method: string, data: Record<string, any>) => Promise<boolean>
  getPaymentMethods: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing payments
 */
export function usePayments(): UsePaymentsReturn {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const getPaymentMethods = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/payments/methods')
      const result: ApiResponse<PaymentMethod[]> = await response.json()

      if (result.success && result.data) {
        setPaymentMethods(result.data)
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch payment methods' })
        setPaymentMethods([])
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setPaymentMethods([])
    } finally {
      setLoading(false)
    }
  }, [])

  const createPayment = useCallback(async (method: string, request: PaymentRequest): Promise<PaymentResponse | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/payments/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method,
          ...request
        }),
      })

      const result: ApiResponse<PaymentResponse> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'PAYMENT_ERROR', message: 'Failed to create payment' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const verifyPayment = useCallback(async (method: string, data: Record<string, any>): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/payments/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method,
          data
        }),
      })

      const result: ApiResponse<{ verified: boolean }> = await response.json()

      if (result.success && result.data) {
        return result.data.verified || false
      } else {
        setError(result.error || { code: 'VERIFICATION_ERROR', message: 'Failed to verify payment' })
        return false
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    paymentMethods,
    loading,
    error,
    createPayment,
    verifyPayment,
    getPaymentMethods,
    clearError
  }
}

export interface UseCheckoutReturn {
  processCheckout: (checkoutData: CheckoutData) => Promise<CheckoutResult | null>
  loading: boolean
  error: { code: string; message: string } | null
  clearError: () => void
}

export interface CheckoutData {
  // Customer information
  customerEmail: string
  customerPhone?: string
  customerName: string
  
  // Addresses
  shippingAddress: {
    firstName: string
    lastName: string
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
    phone?: string
  }
  billingAddress?: {
    firstName: string
    lastName: string
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
    phone?: string
  }
  
  // Shipping and payment
  shippingMethod: string
  paymentMethod: string
  
  // Cart items
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    price: number
    name: string
    image?: string
    color?: string
    size?: string
  }>
  
  // Optional
  couponCode?: string
  notes?: string
}

export interface CheckoutResult {
  success: boolean
  orderId: string
  paymentUrl?: string
  error?: string
}

/**
 * Hook for managing checkout process
 */
export function useCheckout(): UseCheckoutReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const processCheckout = useCallback(async (checkoutData: CheckoutData): Promise<CheckoutResult | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(checkoutData),
      })

      const result: ApiResponse<CheckoutResult> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'CHECKOUT_ERROR', message: 'Failed to process checkout' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    processCheckout,
    loading,
    error,
    clearError
  }
}
