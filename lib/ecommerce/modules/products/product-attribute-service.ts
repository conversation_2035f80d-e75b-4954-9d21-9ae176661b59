// Product Attribute Service
import { PrismaClient } from '@prisma/client'
import { ApiResponse } from '../../types/base'

const prisma = new PrismaClient()

export interface ProductAttribute {
  id: string
  name: string
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'color' | 'size'
  required: boolean
  options?: string[]
  description?: string
  displayOrder: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CreateAttributeInput {
  name: string
  type: ProductAttribute['type']
  required?: boolean
  options?: string[]
  description?: string
  displayOrder?: number
  isActive?: boolean
}

export interface UpdateAttributeInput extends Partial<CreateAttributeInput> {
  id: string
}

export class ProductAttributeService {
  async getAllAttributes(): Promise<ApiResponse<ProductAttribute[]>> {
    try {
      const attributes = await prisma.productAttribute.findMany({
        orderBy: {
          displayOrder: 'asc'
        }
      })

      return {
        success: true,
        data: attributes.map(this.mapToProductAttribute)
      }
    } catch (error) {
      console.error('Get attributes error:', error)
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch product attributes'
        }
      }
    }
  }

  async getAttributeById(id: string): Promise<ApiResponse<ProductAttribute>> {
    try {
      const attribute = await prisma.productAttribute.findUnique({
        where: { id }
      })

      if (!attribute) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product attribute not found'
          }
        }
      }

      return {
        success: true,
        data: this.mapToProductAttribute(attribute)
      }
    } catch (error) {
      console.error('Get attribute error:', error)
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch product attribute'
        }
      }
    }
  }

  async createAttribute(input: CreateAttributeInput): Promise<ApiResponse<ProductAttribute>> {
    try {
      // Validate input
      const validation = this.validateAttributeInput(input)
      if (!validation.isValid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: validation.error || 'Invalid attribute data'
          }
        }
      }

      // Check if attribute name already exists
      const existing = await prisma.productAttribute.findFirst({
        where: {
          name: {
            equals: input.name,
            mode: 'insensitive'
          }
        }
      })

      if (existing) {
        return {
          success: false,
          error: {
            code: 'DUPLICATE_NAME',
            message: 'An attribute with this name already exists'
          }
        }
      }

      // Get next display order if not provided
      let displayOrder = input.displayOrder
      if (displayOrder === undefined) {
        const lastAttribute = await prisma.productAttribute.findFirst({
          orderBy: { displayOrder: 'desc' }
        })
        displayOrder = (lastAttribute?.displayOrder || 0) + 1
      }

      const attribute = await prisma.productAttribute.create({
        data: {
          name: input.name,
          type: input.type,
          required: input.required || false,
          options: input.options || [],
          description: input.description,
          displayOrder,
          isActive: input.isActive !== false
        }
      })

      return {
        success: true,
        data: this.mapToProductAttribute(attribute)
      }
    } catch (error) {
      console.error('Create attribute error:', error)
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create product attribute'
        }
      }
    }
  }

  async updateAttribute(input: UpdateAttributeInput): Promise<ApiResponse<ProductAttribute>> {
    try {
      // Check if attribute exists
      const existing = await prisma.productAttribute.findUnique({
        where: { id: input.id }
      })

      if (!existing) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product attribute not found'
          }
        }
      }

      // Validate input if provided
      if (input.name || input.type) {
        const validation = this.validateAttributeInput({
          name: input.name || existing.name,
          type: input.type || existing.type as any,
          options: input.options || existing.options
        })
        
        if (!validation.isValid) {
          return {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: validation.error || 'Invalid attribute data'
            }
          }
        }
      }

      // Check for duplicate name if name is being changed
      if (input.name && input.name !== existing.name) {
        const duplicate = await prisma.productAttribute.findFirst({
          where: {
            name: {
              equals: input.name,
              mode: 'insensitive'
            },
            id: {
              not: input.id
            }
          }
        })

        if (duplicate) {
          return {
            success: false,
            error: {
              code: 'DUPLICATE_NAME',
              message: 'An attribute with this name already exists'
            }
          }
        }
      }

      const updateData: any = {}
      if (input.name !== undefined) updateData.name = input.name
      if (input.type !== undefined) updateData.type = input.type
      if (input.required !== undefined) updateData.required = input.required
      if (input.options !== undefined) updateData.options = input.options
      if (input.description !== undefined) updateData.description = input.description
      if (input.displayOrder !== undefined) updateData.displayOrder = input.displayOrder
      if (input.isActive !== undefined) updateData.isActive = input.isActive

      const attribute = await prisma.productAttribute.update({
        where: { id: input.id },
        data: updateData
      })

      return {
        success: true,
        data: this.mapToProductAttribute(attribute)
      }
    } catch (error) {
      console.error('Update attribute error:', error)
      return {
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to update product attribute'
        }
      }
    }
  }

  async deleteAttribute(id: string): Promise<ApiResponse<boolean>> {
    try {
      // Check if attribute exists
      const existing = await prisma.productAttribute.findUnique({
        where: { id }
      })

      if (!existing) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product attribute not found'
          }
        }
      }

      // Check if attribute is being used by any products
      // This would require checking product variants or attribute values
      // For now, we'll allow deletion

      await prisma.productAttribute.delete({
        where: { id }
      })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete attribute error:', error)
      return {
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: 'Failed to delete product attribute'
        }
      }
    }
  }

  private validateAttributeInput(input: Partial<CreateAttributeInput>): { isValid: boolean; error?: string } {
    if (!input.name || input.name.trim().length === 0) {
      return { isValid: false, error: 'Attribute name is required' }
    }

    if (input.name.length > 100) {
      return { isValid: false, error: 'Attribute name must be 100 characters or less' }
    }

    if (!input.type) {
      return { isValid: false, error: 'Attribute type is required' }
    }

    const validTypes = ['text', 'number', 'boolean', 'select', 'multiselect', 'color', 'size']
    if (!validTypes.includes(input.type)) {
      return { isValid: false, error: 'Invalid attribute type' }
    }

    // Validate options for select types
    if ((input.type === 'select' || input.type === 'multiselect') && (!input.options || input.options.length === 0)) {
      return { isValid: false, error: 'Options are required for select and multiselect types' }
    }

    return { isValid: true }
  }

  private mapToProductAttribute(attribute: any): ProductAttribute {
    return {
      id: attribute.id,
      name: attribute.name,
      type: attribute.type,
      required: attribute.required,
      options: attribute.options,
      description: attribute.description,
      displayOrder: attribute.displayOrder,
      isActive: attribute.isActive,
      createdAt: attribute.createdAt,
      updatedAt: attribute.updatedAt
    }
  }
}

export const productAttributeService = new ProductAttributeService()
