// Product Bundle Service
import { PrismaClient } from '@prisma/client'
import { ApiResponse } from '../../types/base'

const prisma = new PrismaClient()

export interface ProductBundle {
  id: string
  name: string
  description?: string
  bundleType: 'fixed' | 'flexible'
  discountType: 'percentage' | 'fixed_amount'
  discountValue: number
  minQuantity?: number
  maxQuantity?: number
  isActive: boolean
  products: BundleProduct[]
  createdAt: Date
  updatedAt: Date
}

export interface BundleProduct {
  id: string
  productId: string
  quantity: number
  isRequired: boolean
  product?: {
    id: string
    title: string
    price: number
    image?: string
  }
}

export interface CreateBundleInput {
  name: string
  description?: string
  bundleType: 'fixed' | 'flexible'
  discountType: 'percentage' | 'fixed_amount'
  discountValue: number
  minQuantity?: number
  maxQuantity?: number
  isActive?: boolean
  products: Array<{
    productId: string
    quantity: number
    isRequired?: boolean
  }>
}

export interface UpdateBundleInput extends Partial<CreateBundleInput> {
  id: string
}

export class ProductBundleService {
  async getAllBundles(): Promise<ApiResponse<ProductBundle[]>> {
    try {
      const bundles = await prisma.productBundle.findMany({
        include: {
          products: {
            include: {
              product: {
                select: {
                  id: true,
                  title: true,
                  price: true,
                  images: {
                    take: 1,
                    select: {
                      url: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return {
        success: true,
        data: bundles.map(this.mapToProductBundle)
      }
    } catch (error) {
      console.error('Get bundles error:', error)
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch product bundles'
        }
      }
    }
  }

  async getBundleById(id: string): Promise<ApiResponse<ProductBundle>> {
    try {
      const bundle = await prisma.productBundle.findUnique({
        where: { id },
        include: {
          products: {
            include: {
              product: {
                select: {
                  id: true,
                  title: true,
                  price: true,
                  images: {
                    take: 1,
                    select: {
                      url: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!bundle) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product bundle not found'
          }
        }
      }

      return {
        success: true,
        data: this.mapToProductBundle(bundle)
      }
    } catch (error) {
      console.error('Get bundle error:', error)
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch product bundle'
        }
      }
    }
  }

  async createBundle(input: CreateBundleInput): Promise<ApiResponse<ProductBundle>> {
    try {
      // Validate input
      const validation = this.validateBundleInput(input)
      if (!validation.isValid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: validation.error || 'Invalid bundle data'
          }
        }
      }

      // Check if bundle name already exists
      const existing = await prisma.productBundle.findFirst({
        where: {
          name: {
            equals: input.name,
            mode: 'insensitive'
          }
        }
      })

      if (existing) {
        return {
          success: false,
          error: {
            code: 'DUPLICATE_NAME',
            message: 'A bundle with this name already exists'
          }
        }
      }

      // Verify all products exist
      const productIds = input.products.map(p => p.productId)
      const products = await prisma.product.findMany({
        where: {
          id: { in: productIds }
        }
      })

      if (products.length !== productIds.length) {
        return {
          success: false,
          error: {
            code: 'INVALID_PRODUCTS',
            message: 'One or more products do not exist'
          }
        }
      }

      const bundle = await prisma.productBundle.create({
        data: {
          name: input.name,
          description: input.description,
          bundleType: input.bundleType,
          discountType: input.discountType,
          discountValue: input.discountValue,
          minQuantity: input.minQuantity,
          maxQuantity: input.maxQuantity,
          isActive: input.isActive !== false,
          products: {
            create: input.products.map(p => ({
              productId: p.productId,
              quantity: p.quantity,
              isRequired: p.isRequired !== false
            }))
          }
        },
        include: {
          products: {
            include: {
              product: {
                select: {
                  id: true,
                  title: true,
                  price: true,
                  images: {
                    take: 1,
                    select: {
                      url: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      return {
        success: true,
        data: this.mapToProductBundle(bundle)
      }
    } catch (error) {
      console.error('Create bundle error:', error)
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create product bundle'
        }
      }
    }
  }

  async updateBundle(input: UpdateBundleInput): Promise<ApiResponse<ProductBundle>> {
    try {
      // Check if bundle exists
      const existing = await prisma.productBundle.findUnique({
        where: { id: input.id }
      })

      if (!existing) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product bundle not found'
          }
        }
      }

      // Validate input if provided
      if (input.name || input.bundleType || input.discountType !== undefined) {
        const validation = this.validateBundleInput({
          name: input.name || existing.name,
          bundleType: input.bundleType || existing.bundleType as any,
          discountType: input.discountType || existing.discountType as any,
          discountValue: input.discountValue !== undefined ? input.discountValue : existing.discountValue,
          products: input.products || []
        })
        
        if (!validation.isValid) {
          return {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: validation.error || 'Invalid bundle data'
            }
          }
        }
      }

      const updateData: any = {}
      if (input.name !== undefined) updateData.name = input.name
      if (input.description !== undefined) updateData.description = input.description
      if (input.bundleType !== undefined) updateData.bundleType = input.bundleType
      if (input.discountType !== undefined) updateData.discountType = input.discountType
      if (input.discountValue !== undefined) updateData.discountValue = input.discountValue
      if (input.minQuantity !== undefined) updateData.minQuantity = input.minQuantity
      if (input.maxQuantity !== undefined) updateData.maxQuantity = input.maxQuantity
      if (input.isActive !== undefined) updateData.isActive = input.isActive

      // Handle products update if provided
      if (input.products) {
        // Delete existing products and create new ones
        await prisma.bundleProduct.deleteMany({
          where: { bundleId: input.id }
        })

        updateData.products = {
          create: input.products.map(p => ({
            productId: p.productId,
            quantity: p.quantity,
            isRequired: p.isRequired !== false
          }))
        }
      }

      const bundle = await prisma.productBundle.update({
        where: { id: input.id },
        data: updateData,
        include: {
          products: {
            include: {
              product: {
                select: {
                  id: true,
                  title: true,
                  price: true,
                  images: {
                    take: 1,
                    select: {
                      url: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      return {
        success: true,
        data: this.mapToProductBundle(bundle)
      }
    } catch (error) {
      console.error('Update bundle error:', error)
      return {
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to update product bundle'
        }
      }
    }
  }

  async deleteBundle(id: string): Promise<ApiResponse<boolean>> {
    try {
      // Check if bundle exists
      const existing = await prisma.productBundle.findUnique({
        where: { id }
      })

      if (!existing) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product bundle not found'
          }
        }
      }

      // Delete bundle and related products (cascade)
      await prisma.productBundle.delete({
        where: { id }
      })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete bundle error:', error)
      return {
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: 'Failed to delete product bundle'
        }
      }
    }
  }

  private validateBundleInput(input: Partial<CreateBundleInput>): { isValid: boolean; error?: string } {
    if (!input.name || input.name.trim().length === 0) {
      return { isValid: false, error: 'Bundle name is required' }
    }

    if (!input.bundleType) {
      return { isValid: false, error: 'Bundle type is required' }
    }

    if (!['fixed', 'flexible'].includes(input.bundleType)) {
      return { isValid: false, error: 'Invalid bundle type' }
    }

    if (!input.discountType) {
      return { isValid: false, error: 'Discount type is required' }
    }

    if (!['percentage', 'fixed_amount'].includes(input.discountType)) {
      return { isValid: false, error: 'Invalid discount type' }
    }

    if (input.discountValue === undefined || input.discountValue < 0) {
      return { isValid: false, error: 'Discount value must be a positive number' }
    }

    if (input.discountType === 'percentage' && input.discountValue > 100) {
      return { isValid: false, error: 'Percentage discount cannot exceed 100%' }
    }

    return { isValid: true }
  }

  private mapToProductBundle(bundle: any): ProductBundle {
    return {
      id: bundle.id,
      name: bundle.name,
      description: bundle.description,
      bundleType: bundle.bundleType,
      discountType: bundle.discountType,
      discountValue: bundle.discountValue,
      minQuantity: bundle.minQuantity,
      maxQuantity: bundle.maxQuantity,
      isActive: bundle.isActive,
      products: bundle.products?.map((bp: any) => ({
        id: bp.id,
        productId: bp.productId,
        quantity: bp.quantity,
        isRequired: bp.isRequired,
        product: bp.product ? {
          id: bp.product.id,
          title: bp.product.title,
          price: bp.product.price,
          image: bp.product.images?.[0]?.url
        } : undefined
      })) || [],
      createdAt: bundle.createdAt,
      updatedAt: bundle.updatedAt
    }
  }
}

export const productBundleService = new ProductBundleService()
