// Enhanced Product Variant Service
import { PrismaClient } from '@prisma/client'
import { ApiResponse } from '../../types/base'

const prisma = new PrismaClient()

export interface ProductVariant {
  id: string
  productId: string
  sku: string
  title: string
  price: number
  compareAtPrice?: number
  currency: string
  weight?: number
  weightUnit?: string
  inventoryQuantity: number
  inventoryPolicy: string
  fulfillmentService: string
  inventoryManagement: boolean
  imageId?: string
  available: boolean
  position: number
  barcode?: string
  continueSellingWhenOutOfStock: boolean
  costPerItem?: number
  metafields?: any
  requiresShipping: boolean
  taxable: boolean
  trackQuantity: boolean
  options: VariantOption[]
  createdAt: Date
  updatedAt: Date
}

export interface VariantOption {
  id: string
  name: string
  value: string
}

export interface CreateVariantInput {
  productId: string
  sku?: string
  title: string
  price: number
  compareAtPrice?: number
  currency?: string
  weight?: number
  weightUnit?: string
  inventoryQuantity?: number
  inventoryPolicy?: string
  fulfillmentService?: string
  inventoryManagement?: boolean
  imageId?: string
  available?: boolean
  position?: number
  barcode?: string
  continueSellingWhenOutOfStock?: boolean
  costPerItem?: number
  metafields?: any
  requiresShipping?: boolean
  taxable?: boolean
  trackQuantity?: boolean
  options?: Array<{ name: string; value: string }>
}

export interface UpdateVariantInput extends Partial<CreateVariantInput> {
  id: string
}

export class EnhancedVariantService {
  async getVariantsByProduct(productId: string): Promise<ApiResponse<ProductVariant[]>> {
    try {
      const variants = await prisma.productVariant.findMany({
        where: { productId },
        orderBy: { position: 'asc' },
        include: {
          options: true
        }
      })

      return {
        success: true,
        data: variants.map(this.mapToProductVariant)
      }
    } catch (error) {
      console.error('Get variants error:', error)
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch product variants'
        }
      }
    }
  }

  async getVariantById(id: string): Promise<ApiResponse<ProductVariant>> {
    try {
      const variant = await prisma.productVariant.findUnique({
        where: { id }
      })

      if (!variant) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product variant not found'
          }
        }
      }

      return {
        success: true,
        data: this.mapToProductVariant(variant)
      }
    } catch (error) {
      console.error('Get variant error:', error)
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch product variant'
        }
      }
    }
  }

  async createVariant(input: CreateVariantInput): Promise<ApiResponse<ProductVariant>> {
    try {
      // Validate input
      const validation = this.validateVariantInput(input)
      if (!validation.isValid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: validation.error || 'Invalid variant data'
          }
        }
      }

      // Check if product exists
      const product = await prisma.product.findUnique({
        where: { id: input.productId }
      })

      if (!product) {
        return {
          success: false,
          error: {
            code: 'PRODUCT_NOT_FOUND',
            message: 'Product not found'
          }
        }
      }

      // Generate SKU if not provided
      const sku = input.sku || await this.generateSKU(input.productId)

      // Check for duplicate SKU
      const existingSku = await prisma.productVariant.findFirst({
        where: { sku }
      })

      if (existingSku) {
        return {
          success: false,
          error: {
            code: 'DUPLICATE_SKU',
            message: 'A variant with this SKU already exists'
          }
        }
      }

      // Get next position
      const lastVariant = await prisma.productVariant.findFirst({
        where: { productId: input.productId },
        orderBy: { position: 'desc' }
      })
      const position = (lastVariant?.position || 0) + 1

      const variant = await prisma.productVariant.create({
        data: {
          productId: input.productId,
          sku,
          title: input.title,
          price: input.price,
          compareAtPrice: input.compareAtPrice,
          currency: input.currency || 'ZAR',
          weight: input.weight,
          weightUnit: input.weightUnit,
          inventoryQuantity: input.inventoryQuantity || 0,
          inventoryPolicy: input.inventoryPolicy || 'deny',
          fulfillmentService: input.fulfillmentService || 'manual',
          inventoryManagement: input.inventoryManagement ?? true,
          imageId: input.imageId,
          available: input.available ?? true,
          position,
          barcode: input.barcode,
          continueSellingWhenOutOfStock: input.continueSellingWhenOutOfStock ?? false,
          costPerItem: input.costPerItem,
          metafields: input.metafields,
          requiresShipping: input.requiresShipping ?? true,
          taxable: input.taxable ?? true,
          trackQuantity: input.trackQuantity ?? true,
          options: {
            create: input.options?.map(option => ({
              name: option.name,
              value: option.value
            })) || []
          }
        },
        include: {
          options: true
        }
      })

      return {
        success: true,
        data: this.mapToProductVariant(variant)
      }
    } catch (error) {
      console.error('Create variant error:', error)
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create product variant'
        }
      }
    }
  }

  async updateVariant(input: UpdateVariantInput): Promise<ApiResponse<ProductVariant>> {
    try {
      // Check if variant exists
      const existing = await prisma.productVariant.findUnique({
        where: { id: input.id }
      })

      if (!existing) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product variant not found'
          }
        }
      }

      // Validate input if provided
      if (input.title || input.price !== undefined) {
        const validation = this.validateVariantInput({
          productId: existing.productId,
          title: input.title || existing.title,
          price: input.price !== undefined ? input.price : Number(existing.price)
        })
        
        if (!validation.isValid) {
          return {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: validation.error || 'Invalid variant data'
            }
          }
        }
      }

      // Check for duplicate SKU if SKU is being changed
      if (input.sku && input.sku !== existing.sku) {
        const duplicate = await prisma.productVariant.findFirst({
          where: {
            sku: input.sku,
            id: { not: input.id }
          }
        })

        if (duplicate) {
          return {
            success: false,
            error: {
              code: 'DUPLICATE_SKU',
              message: 'A variant with this SKU already exists'
            }
          }
        }
      }

      const updateData: any = {}
      if (input.sku !== undefined) updateData.sku = input.sku
      if (input.title !== undefined) updateData.title = input.title
      if (input.price !== undefined) updateData.price = input.price
      if (input.compareAtPrice !== undefined) updateData.compareAtPrice = input.compareAtPrice
      if (input.costPerItem !== undefined) updateData.costPerItem = input.costPerItem
      if (input.weight !== undefined) updateData.weight = input.weight
      if (input.weightUnit !== undefined) updateData.weightUnit = input.weightUnit
      if (input.requiresShipping !== undefined) updateData.requiresShipping = input.requiresShipping
      if (input.taxable !== undefined) updateData.taxable = input.taxable
      if (input.inventoryQuantity !== undefined) updateData.inventoryQuantity = input.inventoryQuantity
      if (input.inventoryPolicy !== undefined) updateData.inventoryPolicy = input.inventoryPolicy
      if (input.fulfillmentService !== undefined) updateData.fulfillmentService = input.fulfillmentService
      if (input.inventoryManagement !== undefined) updateData.inventoryManagement = input.inventoryManagement
      if (input.available !== undefined) updateData.available = input.available
      if (input.barcode !== undefined) updateData.barcode = input.barcode
      if (input.continueSellingWhenOutOfStock !== undefined) updateData.continueSellingWhenOutOfStock = input.continueSellingWhenOutOfStock
      if (input.metafields !== undefined) updateData.metafields = input.metafields
      if (input.trackQuantity !== undefined) updateData.trackQuantity = input.trackQuantity
      if (input.imageId !== undefined) updateData.imageId = input.imageId

      // Handle options update separately if provided
      if (input.options !== undefined) {
        // Delete existing options and create new ones
        await prisma.variantOption.deleteMany({
          where: { variantId: input.id }
        })
        
        if (input.options.length > 0) {
          updateData.options = {
            create: input.options.map(option => ({
              name: option.name,
              value: option.value
            }))
          }
        }
      }

      const variant = await prisma.productVariant.update({
        where: { id: input.id },
        data: updateData,
        include: {
          options: true
        }
      })

      return {
        success: true,
        data: this.mapToProductVariant(variant)
      }
    } catch (error) {
      console.error('Update variant error:', error)
      return {
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to update product variant'
        }
      }
    }
  }

  async deleteVariant(id: string): Promise<ApiResponse<boolean>> {
    try {
      // Check if variant exists
      const existing = await prisma.productVariant.findUnique({
        where: { id }
      })

      if (!existing) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product variant not found'
          }
        }
      }

      await prisma.productVariant.delete({
        where: { id }
      })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete variant error:', error)
      return {
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: 'Failed to delete product variant'
        }
      }
    }
  }

  private async generateSKU(productId: string): Promise<string> {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { title: true }
    })

    const prefix = product?.title
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 3)
      .toUpperCase() || 'VAR'

    const timestamp = Date.now().toString().slice(-6)
    return `${prefix}-${timestamp}`
  }

  private validateVariantInput(input: Partial<CreateVariantInput>): { isValid: boolean; error?: string } {
    if (!input.productId) {
      return { isValid: false, error: 'Product ID is required' }
    }

    if (!input.title || input.title.trim().length === 0) {
      return { isValid: false, error: 'Variant title is required' }
    }

    if (input.price === undefined || input.price < 0) {
      return { isValid: false, error: 'Valid price is required' }
    }

    if (input.compareAtPrice !== undefined && input.compareAtPrice < input.price) {
      return { isValid: false, error: 'Compare at price must be greater than or equal to price' }
    }

    return { isValid: true }
  }

  private mapToProductVariant(variant: any): ProductVariant {
    return {
      id: variant.id,
      productId: variant.productId,
      sku: variant.sku,
      title: variant.title,
      price: Number(variant.price),
      compareAtPrice: variant.compareAtPrice ? Number(variant.compareAtPrice) : undefined,
      currency: variant.currency,
      weight: variant.weight ? Number(variant.weight) : undefined,
      weightUnit: variant.weightUnit,
      inventoryQuantity: variant.inventoryQuantity,
      inventoryPolicy: variant.inventoryPolicy,
      fulfillmentService: variant.fulfillmentService,
      inventoryManagement: variant.inventoryManagement,
      imageId: variant.imageId,
      available: variant.available,
      position: variant.position,
      barcode: variant.barcode,
      continueSellingWhenOutOfStock: variant.continueSellingWhenOutOfStock,
      costPerItem: variant.costPerItem ? Number(variant.costPerItem) : undefined,
      metafields: variant.metafields,
      requiresShipping: variant.requiresShipping,
      taxable: variant.taxable,
      trackQuantity: variant.trackQuantity,
      options: variant.options?.map((option: any) => ({
        id: option.id,
        name: option.name,
        value: option.value
      })) || [],
      createdAt: variant.createdAt,
      updatedAt: variant.updatedAt
    }
  }
}

export const enhancedVariantService = new EnhancedVariantService()
