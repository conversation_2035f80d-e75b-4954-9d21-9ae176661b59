import { PrismaClient } from '@prisma/client'
import crypto from 'crypto'
import { settingsService } from '../../settings/settings-service'

const prisma = new PrismaClient()

// Enhanced payment method types for South African market
export interface PaymentMethod {
  id: string
  name: string
  type: 'payfast' | 'ozow' | 'card' | 'eft' | 'snapscan' | 'zapper' | 'cash'
  enabled: boolean
  config: Record<string, any>
  description?: string
  icon?: string
  fees?: {
    percentage?: number
    fixed?: number
    minimum?: number
    maximum?: number
  }
  supportedBanks?: string[]
  processingTime?: string
}

export interface PaymentRequest {
  orderId: string
  amount: number
  currency: 'ZAR'
  customerEmail: string
  customerName: string
  description: string
  returnUrl: string
  cancelUrl: string
  notifyUrl: string
  customerPhone?: string
  billingAddress?: {
    address1: string
    city: string
    province: string
    postalCode: string
    country: string
  }
  metadata?: Record<string, any>
}

export interface PaymentResponse {
  success: boolean
  paymentId: string
  redirectUrl?: string
  qrCode?: string
  instructions?: string
  error?: string
  errorCode?: string
  retryable?: boolean
}

// Enhanced error handling
export class PaymentError extends Error {
  constructor(
    message: string,
    public code: string,
    public retryable: boolean = false,
    public details?: Record<string, any>
  ) {
    super(message)
    this.name = 'PaymentError'
  }
}

// Payment status enum
export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded'
}

// Enhanced PayFast Integration with dynamic configuration
export class PayFastService {
  private merchantId: string
  private merchantKey: string
  private passphrase: string
  private sandbox: boolean
  private maxRetries: number = 3
  private retryDelay: number = 1000
  private configLoaded: boolean = false

  constructor() {
    // Initialize with environment variables as fallback
    this.merchantId = process.env.PAYFAST_MERCHANT_ID || ''
    this.merchantKey = process.env.PAYFAST_MERCHANT_KEY || ''
    this.passphrase = process.env.PAYFAST_PASSPHRASE || ''
    this.sandbox = process.env.NODE_ENV !== 'production'

    // Try to load from settings without throwing error
    this.loadConfigFromSettings()
  }

  private async loadConfigFromSettings(): Promise<void> {
    try {
      // Try to get settings from database
      const settings = await this.getPaymentSettings()

      if (settings?.payfast) {
        this.merchantId = settings.payfast.merchantId || this.merchantId
        this.merchantKey = settings.payfast.merchantKey || this.merchantKey
        this.passphrase = settings.payfast.passphrase || this.passphrase
        this.sandbox = settings.payfast.testMode ?? this.sandbox
        this.configLoaded = true
      }
    } catch (error) {
      // Silently fail and use environment variables or defaults
      console.warn('Could not load PayFast settings from database, using environment variables')
    }
  }

  private async getPaymentSettings(): Promise<any> {
    try {
      // Use the settings service to get payment settings
      return await settingsService.getPaymentSettings()
    } catch (error) {
      // Database might not be ready or settings table doesn't exist
      console.warn('Could not load payment settings, using defaults:', error)
    }

    // Return default settings
    return {
      payfast: {
        merchantId: '10000100', // Default test merchant ID
        merchantKey: '46f0cd694581a', // Default test merchant key
        passphrase: '',
        testMode: true,
        enabled: true
      }
    }
  }

  private async ensureConfigLoaded(): Promise<void> {
    if (!this.configLoaded) {
      await this.loadConfigFromSettings()
    }
  }

  private async validateConfiguration(): Promise<void> {
    await this.ensureConfigLoaded()

    // Use default test credentials if none are configured
    if (!this.merchantId || !this.merchantKey) {
      console.warn('PayFast: No configuration found, using default test credentials')
      this.merchantId = '10000100' // PayFast test merchant ID
      this.merchantKey = '46f0cd694581a' // PayFast test merchant key
      this.passphrase = ''
      this.sandbox = true
    }
  }

  private validateAmount(amount: number): void {
    if (amount < 5) {
      throw new PaymentError(
        'PayFast minimum amount is R5.00',
        'PAYFAST_AMOUNT_TOO_LOW',
        false
      )
    }
    if (amount > 1000000) {
      throw new PaymentError(
        'PayFast maximum amount is R1,000,000.00',
        'PAYFAST_AMOUNT_TOO_HIGH',
        false
      )
    }
  }

  private generateSignature(data: Record<string, any>): string {
    try {
      // Remove signature if it exists
      const cleanData = { ...data }
      delete cleanData.signature

      // Create parameter string
      const paramString = Object.keys(cleanData)
        .sort()
        .map(key => `${key}=${encodeURIComponent(cleanData[key])}`)
        .join('&')

      // Add passphrase if provided
      const stringToHash = this.passphrase
        ? `${paramString}&passphrase=${encodeURIComponent(this.passphrase)}`
        : paramString

      return crypto.createHash('md5').update(stringToHash).digest('hex')
    } catch (error) {
      throw new PaymentError(
        'Failed to generate PayFast signature',
        'PAYFAST_SIGNATURE_ERROR',
        true,
        { originalError: error }
      )
    }
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      // Ensure configuration is loaded
      await this.validateConfiguration()

      // Validate amount
      this.validateAmount(request.amount)

      // Validate required fields
      if (!request.customerEmail || !request.customerName) {
        throw new PaymentError(
          'Customer email and name are required for PayFast',
          'PAYFAST_MISSING_CUSTOMER_INFO',
          false
        )
      }

      const nameParts = request.customerName.trim().split(' ')
      const firstName = nameParts[0] || ''
      const lastName = nameParts.slice(1).join(' ') || 'Customer'

      const paymentData = {
        merchant_id: this.merchantId,
        merchant_key: this.merchantKey,
        return_url: request.returnUrl,
        cancel_url: request.cancelUrl,
        notify_url: request.notifyUrl,
        name_first: firstName.substring(0, 100), // PayFast limits
        name_last: lastName.substring(0, 100),
        email_address: request.customerEmail,
        cell_number: request.customerPhone || '',
        m_payment_id: request.orderId,
        amount: request.amount.toFixed(2),
        item_name: request.description.substring(0, 100),
        item_description: request.description.substring(0, 255),
        custom_str1: request.orderId,
        custom_str2: 'ecommerce',
        custom_str3: 'order',
        custom_str4: JSON.stringify(request.metadata || {}),
        custom_str5: new Date().toISOString()
      }

      // Generate signature
      const signature = this.generateSignature({ ...paymentData })
      const paymentDataWithSignature = { ...paymentData, signature }

      // Store payment record with retry logic
      const paymentNumber = `PAY-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      await this.retryOperation(async () => {
        await prisma.payment.create({
          data: {
            paymentNumber,
            orderId: request.orderId,
            amount: request.amount,
            currency: request.currency,
            status: PaymentStatus.PENDING,
            gatewayId: 'payfast',
            gatewayPaymentId: request.orderId,
            metadata: paymentDataWithSignature as any
          }
        })
      })

      const baseUrl = this.sandbox
        ? 'https://sandbox.payfast.co.za/eng/process'
        : 'https://www.payfast.co.za/eng/process'

      const queryString = Object.keys(paymentDataWithSignature)
        .map(key => `${key}=${encodeURIComponent((paymentDataWithSignature as any)[key])}`)
        .join('&')

      return {
        success: true,
        paymentId: request.orderId,
        redirectUrl: `${baseUrl}?${queryString}`,
        instructions: 'You will be redirected to PayFast to complete your payment.'
      }
    } catch (error) {
      console.error('PayFast payment creation error:', error)

      if (error instanceof PaymentError) {
        return {
          success: false,
          paymentId: '',
          error: error.message,
          errorCode: error.code,
          retryable: error.retryable
        }
      }

      return {
        success: false,
        paymentId: '',
        error: 'Failed to create PayFast payment',
        errorCode: 'PAYFAST_UNKNOWN_ERROR',
        retryable: true
      }
    }
  }

  private async retryOperation<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error as Error

        if (attempt === this.maxRetries) {
          throw lastError
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt))
      }
    }

    throw lastError!
  }

  async verifyPayment(data: Record<string, any>): Promise<boolean> {
    try {
      const receivedSignature = data.signature
      delete data.signature

      const calculatedSignature = this.generateSignature(data)
      
      return receivedSignature === calculatedSignature
    } catch (error) {
      console.error('PayFast verification error:', error)
      return false
    }
  }
}

// Ozow Integration
export class OzowService {
  private siteCode: string
  private privateKey: string
  private sandbox: boolean

  constructor() {
    this.siteCode = process.env.OZOW_SITE_CODE || ''
    this.privateKey = process.env.OZOW_PRIVATE_KEY || ''
    // this.apiKey = process.env.OZOW_API_KEY || ''
    this.sandbox = process.env.NODE_ENV !== 'production'
  }

  private generateHashCheck(data: Record<string, any>): string {
    const sortedKeys = Object.keys(data).sort()
    const concatenated = sortedKeys.map(key => data[key]).join('')
    const withKey = concatenated + this.privateKey
    
    return crypto.createHash('sha512').update(withKey.toLowerCase()).digest('hex')
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const transactionReference = `TXN_${request.orderId}_${Date.now()}`
      
      const paymentData = {
        SiteCode: this.siteCode,
        CountryCode: 'ZA',
        CurrencyCode: request.currency,
        Amount: request.amount.toFixed(2),
        TransactionReference: transactionReference,
        BankReference: request.description,
        Customer: request.customerEmail,
        SuccessUrl: request.returnUrl,
        ErrorUrl: request.cancelUrl,
        CancelUrl: request.cancelUrl,
        NotifyUrl: request.notifyUrl,
        IsTest: this.sandbox
      }

      // Generate hash check
      const hashCheck = this.generateHashCheck(paymentData)
      const paymentDataWithHash = { ...paymentData, HashCheck: hashCheck } as any

      // Store payment record
      const paymentNumber = `PAY-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      await prisma.payment.create({
        data: {
          paymentNumber,
          orderId: request.orderId,
          amount: request.amount,
          currency: request.currency,
          status: 'pending',
          gatewayId: 'ozow',
          gatewayPaymentId: transactionReference,
          metadata: paymentDataWithHash
        }
      })

      const baseUrl = this.sandbox
        ? 'https://staging.ozow.com'
        : 'https://pay.ozow.com'

      const queryString = Object.keys(paymentDataWithHash)
        .map(key => `${key}=${encodeURIComponent(paymentDataWithHash[key])}`)
        .join('&')

      return {
        success: true,
        paymentId: transactionReference,
        redirectUrl: `${baseUrl}?${queryString}`
      }
    } catch (error) {
      console.error('Ozow payment creation error:', error)
      return {
        success: false,
        paymentId: '',
        error: 'Failed to create Ozow payment'
      }
    }
  }

  async verifyPayment(data: Record<string, any>): Promise<boolean> {
    try {
      const receivedHash = data.HashCheck
      delete data.HashCheck

      const calculatedHash = this.generateHashCheck(data)
      
      return receivedHash === calculatedHash
    } catch (error) {
      console.error('Ozow verification error:', error)
      return false
    }
  }
}

// Enhanced Main Payment Service with comprehensive South African payment methods
export class PaymentService {
  private payfast: PayFastService | null = null
  private ozow: OzowService | null = null

  constructor() {
    try {
      this.payfast = new PayFastService()
    } catch (error) {
      console.warn('PayFast service initialization failed, will use default configuration:', error)
      // Don't throw error, just log warning
    }

    try {
      this.ozow = new OzowService()
    } catch (error) {
      console.warn('Ozow service initialization failed:', error)
      // Don't throw error, just log warning
    }
  }

  private async ensurePayFastService(): Promise<PayFastService> {
    if (!this.payfast) {
      this.payfast = new PayFastService()
    }
    return this.payfast
  }

  async getAvailablePaymentMethods(): Promise<PaymentMethod[]> {
    return [
      {
        id: 'payfast',
        name: 'PayFast',
        type: 'payfast',
        enabled: true,
        description: 'Credit cards, EFT, and SnapScan',
        icon: '💳',
        fees: {
          percentage: 2.9,
          fixed: 0,
          minimum: 1,
          maximum: 100
        },
        supportedBanks: ['All major South African banks'],
        processingTime: 'Instant to 3 business days',
        config: {
          acceptsCreditCards: true,
          acceptsEFT: true,
          acceptsSnapScan: true
        }
      },
      {
        id: 'ozow',
        name: 'Ozow',
        type: 'ozow',
        enabled: true,
        description: 'Instant EFT payments',
        icon: '🏦',
        fees: {
          percentage: 1.5,
          fixed: 0,
          minimum: 2,
          maximum: 50
        },
        supportedBanks: [
          'ABSA', 'Standard Bank', 'FNB', 'Nedbank', 'Capitec',
          'African Bank', 'Bidvest Bank', 'Discovery Bank',
          'Investec', 'TymeBank', 'Bank Zero'
        ],
        processingTime: 'Instant',
        config: {
          instantVerification: true,
          realTimePayments: true
        }
      },
      {
        id: 'snapscan',
        name: 'SnapScan',
        type: 'snapscan',
        enabled: true,
        description: 'QR code payments',
        icon: '📱',
        fees: {
          percentage: 2.95,
          fixed: 0,
          minimum: 1,
          maximum: 50
        },
        processingTime: 'Instant',
        config: {
          qrCodePayment: true,
          mobileApp: true
        }
      },
      {
        id: 'zapper',
        name: 'Zapper',
        type: 'zapper',
        enabled: true,
        description: 'Mobile wallet payments',
        icon: '⚡',
        fees: {
          percentage: 2.5,
          fixed: 0,
          minimum: 1,
          maximum: 30
        },
        processingTime: 'Instant',
        config: {
          mobileWallet: true,
          loyaltyIntegration: true
        }
      },
      {
        id: 'cash',
        name: 'Cash on Collection',
        type: 'cash',
        enabled: true,
        description: 'Pay when collecting your order',
        icon: '💵',
        fees: {
          percentage: 0,
          fixed: 0
        },
        processingTime: 'On collection',
        config: {
          collectionOnly: true,
          noOnlineFees: true
        }
      }
    ]
  }

  async createPayment(method: string, request: PaymentRequest): Promise<PaymentResponse> {
    switch (method) {
      case 'payfast':
        const payfastService = await this.ensurePayFastService()
        return payfastService.createPayment(request)
      case 'ozow':
        if (!this.ozow) {
          return {
            success: false,
            paymentId: '',
            error: 'Ozow service not available'
          }
        }
        return this.ozow.createPayment(request)
      default:
        return {
          success: false,
          paymentId: '',
          error: 'Unsupported payment method'
        }
    }
  }

  async verifyPayment(method: string, data: Record<string, any>): Promise<boolean> {
    switch (method) {
      case 'payfast':
        const payfastService = await this.ensurePayFastService()
        return payfastService.verifyPayment(data)
      case 'ozow':
        if (!this.ozow) {
          return false
        }
        return this.ozow.verifyPayment(data)
      default:
        return false
    }
  }

  async updatePaymentStatus(paymentId: string, status: string, metadata?: Record<string, any>) {
    try {
      // Find payment by gatewayPaymentId first
      const payment = await prisma.payment.findFirst({
        where: { gatewayPaymentId: paymentId }
      })

      if (payment) {
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            status,
            metadata: metadata ? { ...metadata } : undefined,
            updatedAt: new Date()
          }
        })
      }
    } catch (error) {
      console.error('Payment status update error:', error)
    }
  }
}

// Export singleton instance
export const paymentService = new PaymentService()
