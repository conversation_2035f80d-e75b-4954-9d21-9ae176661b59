import { PrismaClient, Product } from '@prisma/client'

const prisma = new PrismaClient()

export interface InventoryItem {
  id: string
  productId: string
  variantId?: string
  sku: string
  quantity: number
  reserved: number
  available: number
  reorderLevel: number
  reorderQuantity: number
  cost: number
  location?: string
  updatedAt: Date
  product?: Product
}

export interface InventoryReservation {
  id: string
  inventoryItemId: string
  quantity: number
  orderId: string
  expiresAt: Date
  createdAt: Date
}

export interface StockMovement {
  id: string
  inventoryItemId: string
  type: 'in' | 'out' | 'adjustment' | 'reserved' | 'released'
  quantity: number
  reason: string
  reference?: string
  createdAt: Date
}

export class InventoryService {
  private db: PrismaClient

  constructor(database?: PrismaClient) {
    this.db = database || prisma
  }
  async getInventory(productId: string, variantId?: string): Promise<InventoryItem | null> {
    try {
      const inventory = await this.db.inventoryItem.findFirst({
        where: {
          productId,
          variantId: variantId || null
        }
      })

      if (!inventory) return null

      return {
        id: inventory.id,
        productId: inventory.productId,
        variantId: inventory.variantId || undefined,
        sku: inventory.sku,
        quantity: inventory.quantity,
        reserved: inventory.reservedQuantity,
        available: inventory.availableQuantity,
        reorderLevel: inventory.reorderPoint,
        reorderQuantity: inventory.reorderQuantity,
        cost: Number(inventory.costPrice),
        location: inventory.binLocation || undefined,
        updatedAt: inventory.updatedAt
      }
    } catch (error) {
      console.error('Get inventory error:', error)
      return null
    }
  }

  async updateInventory(
    productId: string,
    variantId: string | undefined,
    quantity: number,
    cost?: number
  ): Promise<InventoryItem | null> {
    try {
      // Get default location
      const defaultLocation = await this.getDefaultLocation()

      const inventory = await this.db.inventoryItem.upsert({
        where: {
          sku: await this.generateSKU(productId, variantId)
        },
        update: {
          quantity,
          availableQuantity: quantity,
          costPrice: cost || undefined,
          updatedAt: new Date()
        },
        create: {
          productId,
          variantId,
          sku: await this.generateSKU(productId, variantId),
          name: `Product ${productId}`,
          quantity,
          reservedQuantity: 0,
          availableQuantity: quantity,
          committedQuantity: 0,
          reorderPoint: 10,
          reorderQuantity: 50,
          costPrice: cost || 0,
          averageCost: cost || 0,
          currency: 'ZAR',
          locationId: defaultLocation.id
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        inventoryItemId: inventory.id,
        type: 'adjustment',
        quantity,
        reason: 'Inventory update'
      })

      return this.getInventory(productId, variantId)
    } catch (error) {
      console.error('Update inventory error:', error)
      return null
    }
  }

  async adjustInventory(
    productId: string,
    variantId: string | undefined,
    adjustment: number,
    reason: string,
    reference?: string
  ): Promise<InventoryItem | null> {
    try {
      const current = await this.getInventory(productId, variantId)
      if (!current) {
        throw new Error('Inventory item not found')
      }

      const newQuantity = Math.max(0, current.quantity + adjustment)
      const newAvailable = Math.max(0, current.available + adjustment)

      await this.db.inventoryItem.update({
        where: { id: current.id },
        data: {
          quantity: newQuantity,
          availableQuantity: newAvailable,
          updatedAt: new Date()
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        inventoryItemId: current.id,
        type: adjustment > 0 ? 'in' : 'out',
        quantity: Math.abs(adjustment),
        reason,
        reference
      })

      return this.getInventory(productId, variantId)
    } catch (error) {
      console.error('Adjust inventory error:', error)
      return null
    }
  }

  async checkAvailability(productId: string, variantId: string | undefined, quantity: number): Promise<boolean> {
    try {
      const inventory = await this.getInventory(productId, variantId)
      if (!inventory) return false

      return inventory.available >= quantity
    } catch (error) {
      console.error('Check availability error:', error)
      return false
    }
  }

  async reserveInventory(
    productId: string,
    variantId: string | undefined,
    quantity: number,
    orderId: string,
    expirationMinutes = 30
  ): Promise<boolean> {
    try {
      const available = await this.checkAvailability(productId, variantId, quantity)
      if (!available) return false

      const inventory = await this.getInventory(productId, variantId)
      if (!inventory) return false

      const defaultLocation = await this.getDefaultLocation()

      // Create reservation
      const expiresAt = new Date(Date.now() + expirationMinutes * 60 * 1000)

      await this.db.inventoryReservation.create({
        data: {
          inventoryItemId: inventory.id,
          locationId: defaultLocation.id,
          quantity,
          reservationType: 'order',
          referenceType: 'order',
          referenceId: orderId,
          reservedBy: 'system',
          expiresAt
        }
      })

      // Update reserved quantity
      await this.db.inventoryItem.update({
        where: { id: inventory.id },
        data: {
          reservedQuantity: {
            increment: quantity
          },
          availableQuantity: {
            decrement: quantity
          }
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        inventoryItemId: inventory.id,
        type: 'reserved',
        quantity,
        reason: 'Order reservation',
        reference: orderId
      })

      return true
    } catch (error) {
      console.error('Reserve inventory error:', error)
      return false
    }
  }

  async releaseReservation(
    productId: string,
    variantId: string | undefined,
    quantity: number,
    orderId: string
  ): Promise<boolean> {
    try {
      const inventory = await this.getInventory(productId, variantId)
      if (!inventory) return false

      // Remove reservation
      await this.db.inventoryReservation.deleteMany({
        where: {
          inventoryItemId: inventory.id,
          referenceId: orderId
        }
      })

      // Update reserved quantity
      await this.db.inventoryItem.update({
        where: { id: inventory.id },
        data: {
          reservedQuantity: {
            decrement: quantity
          },
          availableQuantity: {
            increment: quantity
          }
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        inventoryItemId: inventory.id,
        type: 'released',
        quantity,
        reason: 'Reservation released',
        reference: orderId
      })

      return true
    } catch (error) {
      console.error('Release reservation error:', error)
      return false
    }
  }

  async commitReservation(
    productId: string,
    variantId: string | undefined,
    quantity: number,
    orderId: string
  ): Promise<boolean> {
    try {
      const inventory = await this.getInventory(productId, variantId)
      if (!inventory) return false

      // Remove reservation
      await this.db.inventoryReservation.deleteMany({
        where: {
          inventoryItemId: inventory.id,
          referenceId: orderId
        }
      })

      // Update quantities
      await this.db.inventoryItem.update({
        where: { id: inventory.id },
        data: {
          quantity: {
            decrement: quantity
          },
          reservedQuantity: {
            decrement: quantity
          },
          committedQuantity: {
            increment: quantity
          }
        }
      })

      // Record stock movement
      await this.recordStockMovement({
        inventoryItemId: inventory.id,
        type: 'out',
        quantity,
        reason: 'Order fulfilled',
        reference: orderId
      })

      return true
    } catch (error) {
      console.error('Commit reservation error:', error)
      return false
    }
  }

  async getStockMovements(
    productId?: string,
    variantId?: string,
    limit = 50,
    offset = 0
  ): Promise<StockMovement[]> {
    try {
      const whereClause: any = {}

      if (productId || variantId) {
        whereClause.inventoryItem = {
          ...(productId && { productId }),
          ...(variantId && { variantId })
        }
      }

      const movements = await this.db.inventoryMovement.findMany({
        where: whereClause,
        include: {
          inventoryItem: true
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      })

      return movements.map(movement => ({
        id: movement.id,
        inventoryItemId: movement.inventoryItemId,
        type: movement.type as StockMovement['type'],
        quantity: movement.quantity,
        reason: movement.reason,
        reference: movement.referenceId || undefined,
        createdAt: movement.createdAt
      }))
    } catch (error) {
      console.error('Get stock movements error:', error)
      return []
    }
  }

  async getLowStockItems(): Promise<InventoryItem[]> {
    try {
      const lowStockItems = await this.db.inventoryItem.findMany({
        where: {
          quantity: {
            lte: this.db.inventoryItem.fields.reorderPoint
          }
        },
        orderBy: { quantity: 'asc' }
      })

      return lowStockItems.map(item => ({
        id: item.id,
        productId: item.productId,
        variantId: item.variantId || undefined,
        sku: item.sku,
        quantity: item.quantity,
        reserved: item.reservedQuantity,
        available: item.availableQuantity,
        reorderLevel: item.reorderPoint,
        reorderQuantity: item.reorderQuantity,
        cost: Number(item.costPrice),
        location: item.binLocation || undefined,
        updatedAt: item.updatedAt
      }))
    } catch (error) {
      console.error('Get low stock items error:', error)
      return []
    }
  }

  async cleanupExpiredReservations(): Promise<number> {
    try {
      const expiredReservations = await this.db.inventoryReservation.findMany({
        where: {
          expiresAt: {
            lt: new Date()
          }
        },
        include: {
          inventoryItem: true
        }
      })

      for (const reservation of expiredReservations) {
        await this.releaseReservation(
          reservation.inventoryItem.productId,
          reservation.inventoryItem.variantId || undefined,
          reservation.quantity,
          reservation.referenceId || 'unknown'
        )
      }

      return expiredReservations.length
    } catch (error) {
      console.error('Cleanup expired reservations error:', error)
      return 0
    }
  }

  private async recordStockMovement(movement: Omit<StockMovement, 'id' | 'createdAt'>): Promise<void> {
    try {
      const defaultLocation = await this.getDefaultLocation()

      await this.db.inventoryMovement.create({
        data: {
          inventoryItemId: movement.inventoryItemId,
          locationId: defaultLocation.id,
          type: movement.type,
          direction: movement.type === 'in' ? 'in' : 'out',
          quantity: movement.quantity,
          reason: movement.reason,
          referenceId: movement.reference
        }
      })
    } catch (error) {
      console.error('Record stock movement error:', error)
    }
  }

  private async getDefaultLocation() {
    let location = await this.db.inventoryLocation.findFirst({
      where: { isPrimary: true }
    })

    if (!location) {
      location = await this.db.inventoryLocation.create({
        data: {
          name: 'Main Warehouse',
          code: 'MAIN',
          type: 'warehouse',
          isPrimary: true,
          isActive: true,
          allowsInventory: true,
          allowsFulfillment: true
        }
      })
    }

    return location
  }

  private async generateSKU(productId: string, variantId?: string): Promise<string> {
    const product = await this.db.product.findUnique({
      where: { id: productId },
      select: { title: true }
    })

    if (!product) throw new Error('Product not found')

    const productCode = product.title
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '')
      .substring(0, 6)

    const variantCode = variantId ? variantId.substring(0, 4).toUpperCase() : '0000'
    const timestamp = Date.now().toString().slice(-6)

    return `${productCode}-${variantCode}-${timestamp}`
  }

  // Add the missing method for inventory movements
  async getInventoryMovements(inventoryItemId: string) {
    try {
      const movements = await this.db.inventoryMovement.findMany({
        where: { inventoryItemId },
        orderBy: { createdAt: 'desc' },
        take: 50
      })

      return {
        success: true,
        data: movements.map(movement => ({
          id: movement.id,
          inventoryItemId: movement.inventoryItemId,
          type: movement.type,
          quantity: movement.quantity,
          reason: movement.reason,
          reference: movement.referenceId,
          createdAt: movement.createdAt
        }))
      }
    } catch (error) {
      console.error('Get inventory movements error:', error)
      return {
        success: false,
        error: { message: 'Failed to fetch inventory movements' }
      }
    }
  }
}

// Export singleton instance
export const inventoryService = new InventoryService()
