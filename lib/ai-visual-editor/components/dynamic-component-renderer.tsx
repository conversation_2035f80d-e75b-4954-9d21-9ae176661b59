'use client'

import { use<PERSON>emo, useCallback, memo, Component } from 'react'
import { GeneratedComponent } from '../types'
import { AlertTriangle } from 'lucide-react'

interface DynamicComponentRendererProps {
  component: GeneratedComponent
  properties: Record<string, any>
  onError?: (error: string) => void
}

// Custom Error Boundary implementation
class ComponentErrorBoundary extends Component<
  {
    children: React.ReactNode
    onError?: (error: string) => void
    componentName: string
  },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error(`Component render error for ${this.props.componentName}:`, error, errorInfo)
    this.props.onError?.(error.message)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2 text-red-800 mb-2">
            <AlertTriangle className="w-5 h-5" />
            <span className="font-medium">Component Error</span>
          </div>
          <p className="text-sm text-red-700">
            Failed to render {this.props.componentName}. Check the component code for syntax errors.
          </p>
        </div>
      )
    }

    return this.props.children
  }
}

export const DynamicComponentRenderer = memo(function DynamicComponentRenderer({
  component,
  properties,
  onError
}: DynamicComponentRendererProps) {

  const renderedComponent = useMemo(() => {
    try {
      // Use safer component rendering approach
      return createSafeReactComponent(component, properties)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown rendering error'
      onError?.(errorMessage)
      return null
    }
  }, [component.jsx, component.name, properties, onError])

  if (!renderedComponent) {
    return (
      <div className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="text-center text-gray-500">
          <div className="text-sm font-medium">Unable to render component</div>
          <div className="text-xs mt-1">Check the component code for errors</div>
        </div>
      </div>
    )
  }

  return (
    <ComponentErrorBoundary onError={onError} componentName={component.name}>
      {renderedComponent}
    </ComponentErrorBoundary>
  )
})

// Safer React component creation using dynamic imports and property injection
function createSafeReactComponent(component: GeneratedComponent, properties: Record<string, any>) {
  // Create a wrapper component that applies properties safely
  const ComponentWrapper = () => {
    const appliedStyles = useMemo(() => generateInlineStyles(properties), [properties])
    const appliedClasses = useMemo(() => generateTailwindClasses(properties), [properties])

    // Parse component JSX and extract structure
    const componentStructure = parseComponentStructure(component.jsx)

    return (
      <div
        className={`ai-generated-component ${appliedClasses}`}
        style={appliedStyles}
        data-component-id={component.id}
        data-component-name={component.name}
      >
        {renderComponentContent(componentStructure, properties)}
      </div>
    )
  }

  return <ComponentWrapper />
}

// Generate safe inline styles from properties
function generateInlineStyles(properties: Record<string, any>): React.CSSProperties {
  const styles: React.CSSProperties = {}

  if (properties.backgroundColor) {
    styles.backgroundColor = sanitizeColor(properties.backgroundColor)
  }

  if (properties.textColor) {
    styles.color = sanitizeColor(properties.textColor)
  }

  if (properties.padding && typeof properties.padding === 'object') {
    const { top, right, bottom, left } = properties.padding
    styles.padding = `${top || 0}px ${right || 0}px ${bottom || 0}px ${left || 0}px`
  }

  if (properties.margin && typeof properties.margin === 'object') {
    const { top, right, bottom, left } = properties.margin
    styles.margin = `${top || 0}px ${right || 0}px ${bottom || 0}px ${left || 0}px`
  }

  if (properties.borderRadius) {
    styles.borderRadius = `${Math.max(0, Math.min(50, Number(properties.borderRadius) || 0))}px`
  }

  if (properties.shadow && properties.shadow !== 'none') {
    styles.boxShadow = sanitizeShadow(properties.shadow)
  }

  return styles
}

// Generate Tailwind classes from properties
function generateTailwindClasses(properties: Record<string, any>): string {
  const classes: string[] = []

  if (properties.width) {
    classes.push(sanitizeTailwindClass(properties.width, 'w-'))
  }

  if (properties.height) {
    classes.push(sanitizeTailwindClass(properties.height, 'h-'))
  }

  if (properties.alignment) {
    classes.push(sanitizeTailwindClass(properties.alignment, 'text-'))
  }

  if (properties.titleSize) {
    classes.push(sanitizeTailwindClass(properties.titleSize, 'text-'))
  }

  if (properties.hoverEffect && properties.hoverEffect !== 'none') {
    classes.push(getHoverEffectClass(properties.hoverEffect))
  }

  if (properties.animation && properties.animation !== 'none') {
    classes.push(getAnimationClass(properties.animation))
  }

  return classes.filter(Boolean).join(' ')
}

// Security and validation utilities
function sanitizeColor(color: string): string {
  // Only allow hex colors and named colors
  if (/^#[0-9A-Fa-f]{6}$/.test(color) || /^#[0-9A-Fa-f]{3}$/.test(color)) {
    return color
  }

  const namedColors = ['red', 'blue', 'green', 'yellow', 'purple', 'pink', 'gray', 'black', 'white']
  if (namedColors.includes(color.toLowerCase())) {
    return color
  }

  return '#000000' // Default fallback
}

function sanitizeShadow(shadow: string): string {
  // Basic shadow validation - only allow specific patterns
  const shadowPattern = /^(\d+px\s+){3}\d+px\s+(rgba?\([^)]+\)|#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3})$/
  if (shadowPattern.test(shadow)) {
    return shadow
  }
  return 'none'
}

function sanitizeTailwindClass(className: string, prefix: string): string {
  // Validate Tailwind classes to prevent injection
  const allowedClasses = {
    'w-': ['w-auto', 'w-full', 'w-1/2', 'w-1/3', 'w-1/4', 'w-[300px]', 'w-[500px]'],
    'h-': ['h-auto', 'h-screen', 'h-[200px]', 'h-[400px]', 'h-[600px]'],
    'text-': ['text-left', 'text-center', 'text-right', 'text-justify', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl', 'text-4xl']
  }

  const allowed = allowedClasses[prefix as keyof typeof allowedClasses] || []
  return allowed.includes(className) ? className : ''
}

// Parse component structure safely
function parseComponentStructure(jsx: string): ComponentStructure {
  // Basic JSX parsing - in production, use a proper JSX parser
  const structure: ComponentStructure = {
    type: 'div',
    props: {},
    children: [],
    content: ''
  }

  // Extract text content safely
  const textMatch = jsx.match(/>([^<]+)</g)
  if (textMatch) {
    structure.content = textMatch.map(match => match.slice(1, -1)).join(' ')
  }

  return structure
}

// Render component content safely
function renderComponentContent(structure: ComponentStructure, properties: Record<string, any>) {
  const content = properties.title || properties.description || structure.content || 'Generated Component'

  return (
    <div className="space-y-4">
      {properties.title && (
        <h2 className="font-bold text-2xl">{sanitizeText(properties.title)}</h2>
      )}
      {properties.description && (
        <p className="text-gray-600">{sanitizeText(properties.description)}</p>
      )}
      {properties.image && (
        <img
          src={sanitizeUrl(properties.image)}
          alt={sanitizeText(properties.imageAlt || 'Generated image')}
          className="max-w-full h-auto rounded"
        />
      )}
      {properties.links && Array.isArray(properties.links) && (
        <div className="space-x-4">
          {properties.links.map((link: any, index: number) => (
            <a
              key={index}
              href={sanitizeUrl(link.url)}
              className="text-blue-600 hover:underline"
            >
              {sanitizeText(link.text)}
            </a>
          ))}
        </div>
      )}
    </div>
  )
}

// Text sanitization
function sanitizeText(text: string): string {
  return text.replace(/<[^>]*>/g, '').slice(0, 500) // Remove HTML and limit length
}

// URL sanitization
function sanitizeUrl(url: string): string {
  try {
    const parsed = new URL(url)
    if (parsed.protocol === 'http:' || parsed.protocol === 'https:') {
      return url
    }
  } catch {
    // Invalid URL
  }
  return '#'
}

interface ComponentStructure {
  type: string
  props: Record<string, any>
  children: ComponentStructure[]
  content: string
}

function getAnimationClass(animation: string): string {
  const animations: Record<string, string> = {
    fadeIn: 'animate-fade-in',
    slideUp: 'animate-slide-up',
    slideDown: 'animate-slide-down',
    scale: 'animate-scale',
    bounce: 'animate-bounce'
  }
  return animations[animation] || ''
}

function getHoverEffectClass(effect: string): string {
  const effects: Record<string, string> = {
    scale: 'hover:scale-105 transition-transform',
    lift: 'hover:-translate-y-1 hover:shadow-lg transition-all',
    glow: 'hover:shadow-lg hover:shadow-blue-500/25 transition-shadow',
    fade: 'hover:opacity-80 transition-opacity'
  }
  return effects[effect] || ''
}
