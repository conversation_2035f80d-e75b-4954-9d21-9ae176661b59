'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Layout, 
  FileCode, 
  Layers, 
  Settings,
  Download,
  Eye,
  Code,
  Sparkles,
  Globe,
  Smartphone,
  Monitor
} from 'lucide-react'
import { NextJSGenerator } from '../services/nextjs-generator'
import { LayoutGenerationParams, PageGenerationParams, NextJSLayout, NextJSPage } from '../types/nextjs-types'
import { toast } from 'sonner'

export function NextJSLayoutGenerator() {
  const [activeTab, setActiveTab] = useState('layout')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedLayouts, setGeneratedLayouts] = useState<NextJSLayout[]>([])
  const [generatedPages, setGeneratedPages] = useState<NextJSPage[]>([])
  
  // Layout form state
  const [layoutParams, setLayoutParams] = useState<LayoutGenerationParams>({
    name: '',
    description: '',
    type: 'root',
    features: [],
    styling: 'modern',
    includeHeader: true,
    includeFooter: true,
    includeSidebar: false,
    responsive: true,
    accessibility: true,
    seo: true
  })
  
  // Page form state
  const [pageParams, setPageParams] = useState<PageGenerationParams>({
    name: '',
    description: '',
    route: '/',
    type: 'static',
    pageType: 'landing',
    features: [],
    styling: 'modern',
    seo: true,
    generateStaticParams: false
  })

  const handleGenerateLayout = async () => {
    if (!layoutParams.name || !layoutParams.description) {
      toast.error('Please fill in all required fields')
      return
    }

    setIsGenerating(true)
    try {
      const layout = await NextJSGenerator.generateLayout(layoutParams)
      setGeneratedLayouts(prev => [layout, ...prev])
      toast.success(`Generated ${layout.name} layout successfully!`)
      
      // Reset form
      setLayoutParams({
        name: '',
        description: '',
        type: 'root',
        features: [],
        styling: 'modern',
        includeHeader: true,
        includeFooter: true,
        includeSidebar: false,
        responsive: true,
        accessibility: true,
        seo: true
      })
    } catch (error) {
      console.error('Layout generation error:', error)
      toast.error('Failed to generate layout')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleGeneratePage = async () => {
    if (!pageParams.name || !pageParams.description || !pageParams.route) {
      toast.error('Please fill in all required fields')
      return
    }

    setIsGenerating(true)
    try {
      const page = await NextJSGenerator.generatePage(pageParams)
      setGeneratedPages(prev => [page, ...prev])
      toast.success(`Generated ${page.name} page successfully!`)
      
      // Reset form
      setPageParams({
        name: '',
        description: '',
        route: '/',
        type: 'static',
        pageType: 'landing',
        features: [],
        styling: 'modern',
        seo: true,
        generateStaticParams: false
      })
    } catch (error) {
      console.error('Page generation error:', error)
      toast.error('Failed to generate page')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleDownloadCode = (item: NextJSLayout | NextJSPage) => {
    const blob = new Blob([item.code], { type: 'text/typescript' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${item.name.toLowerCase().replace(/\s+/g, '-')}.tsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Code downloaded successfully!')
  }

  const renderLayoutForm = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="layout-name">Layout Name *</Label>
          <Input
            id="layout-name"
            placeholder="e.g., Main Layout"
            value={layoutParams.name}
            onChange={(e) => setLayoutParams(prev => ({ ...prev, name: e.target.value }))}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="layout-type">Layout Type</Label>
          <Select
            value={layoutParams.type}
            onValueChange={(value: any) => setLayoutParams(prev => ({ ...prev, type: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="root">Root Layout</SelectItem>
              <SelectItem value="nested">Nested Layout</SelectItem>
              <SelectItem value="template">Template</SelectItem>
              <SelectItem value="group">Route Group</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="layout-description">Description *</Label>
        <Textarea
          id="layout-description"
          placeholder="Describe the layout purpose and structure..."
          value={layoutParams.description}
          onChange={(e) => setLayoutParams(prev => ({ ...prev, description: e.target.value }))}
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label>Styling Approach</Label>
        <Select
          value={layoutParams.styling}
          onValueChange={(value: any) => setLayoutParams(prev => ({ ...prev, styling: value }))}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="modern">Modern</SelectItem>
            <SelectItem value="minimal">Minimal</SelectItem>
            <SelectItem value="bold">Bold</SelectItem>
            <SelectItem value="elegant">Elegant</SelectItem>
            <SelectItem value="playful">Playful</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-4">
        <Label>Layout Components</Label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="include-header"
              checked={layoutParams.includeHeader}
              onCheckedChange={(checked) => setLayoutParams(prev => ({ ...prev, includeHeader: checked }))}
            />
            <Label htmlFor="include-header">Header</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="include-footer"
              checked={layoutParams.includeFooter}
              onCheckedChange={(checked) => setLayoutParams(prev => ({ ...prev, includeFooter: checked }))}
            />
            <Label htmlFor="include-footer">Footer</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="include-sidebar"
              checked={layoutParams.includeSidebar}
              onCheckedChange={(checked) => setLayoutParams(prev => ({ ...prev, includeSidebar: checked }))}
            />
            <Label htmlFor="include-sidebar">Sidebar</Label>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <Label>Features</Label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="responsive"
              checked={layoutParams.responsive}
              onCheckedChange={(checked) => setLayoutParams(prev => ({ ...prev, responsive: checked }))}
            />
            <Label htmlFor="responsive">Responsive Design</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="accessibility"
              checked={layoutParams.accessibility}
              onCheckedChange={(checked) => setLayoutParams(prev => ({ ...prev, accessibility: checked }))}
            />
            <Label htmlFor="accessibility">Accessibility</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="seo"
              checked={layoutParams.seo}
              onCheckedChange={(checked) => setLayoutParams(prev => ({ ...prev, seo: checked }))}
            />
            <Label htmlFor="seo">SEO Optimization</Label>
          </div>
        </div>
      </div>

      <Button 
        onClick={handleGenerateLayout} 
        disabled={isGenerating || !layoutParams.name || !layoutParams.description}
        className="w-full"
      >
        {isGenerating ? (
          <>
            <Sparkles className="w-4 h-4 mr-2 animate-spin" />
            Generating Layout...
          </>
        ) : (
          <>
            <Layout className="w-4 h-4 mr-2" />
            Generate Layout
          </>
        )}
      </Button>
    </div>
  )

  const renderPageForm = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="page-name">Page Name *</Label>
          <Input
            id="page-name"
            placeholder="e.g., Home Page"
            value={pageParams.name}
            onChange={(e) => setPageParams(prev => ({ ...prev, name: e.target.value }))}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="page-route">Route *</Label>
          <Input
            id="page-route"
            placeholder="e.g., /, /products, /blog/[slug]"
            value={pageParams.route}
            onChange={(e) => setPageParams(prev => ({ ...prev, route: e.target.value }))}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="page-type">Page Type</Label>
          <Select
            value={pageParams.type}
            onValueChange={(value: any) => setPageParams(prev => ({ ...prev, type: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="static">Static</SelectItem>
              <SelectItem value="dynamic">Dynamic</SelectItem>
              <SelectItem value="catch-all">Catch All</SelectItem>
              <SelectItem value="optional-catch-all">Optional Catch All</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="page-template">Page Template</Label>
          <Select
            value={pageParams.pageType}
            onValueChange={(value: any) => setPageParams(prev => ({ ...prev, pageType: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="landing">Landing Page</SelectItem>
              <SelectItem value="product">Product Page</SelectItem>
              <SelectItem value="blog">Blog Page</SelectItem>
              <SelectItem value="dashboard">Dashboard</SelectItem>
              <SelectItem value="auth">Authentication</SelectItem>
              <SelectItem value="error">Error Page</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="page-description">Description *</Label>
        <Textarea
          id="page-description"
          placeholder="Describe the page purpose and content..."
          value={pageParams.description}
          onChange={(e) => setPageParams(prev => ({ ...prev, description: e.target.value }))}
          rows={3}
        />
      </div>

      <div className="space-y-4">
        <Label>Page Features</Label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="page-seo"
              checked={pageParams.seo}
              onCheckedChange={(checked) => setPageParams(prev => ({ ...prev, seo: checked }))}
            />
            <Label htmlFor="page-seo">SEO Metadata</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="static-params"
              checked={pageParams.generateStaticParams}
              onCheckedChange={(checked) => setPageParams(prev => ({ ...prev, generateStaticParams: checked }))}
            />
            <Label htmlFor="static-params">Static Generation</Label>
          </div>
        </div>
      </div>

      <Button 
        onClick={handleGeneratePage} 
        disabled={isGenerating || !pageParams.name || !pageParams.description || !pageParams.route}
        className="w-full"
      >
        {isGenerating ? (
          <>
            <Sparkles className="w-4 h-4 mr-2 animate-spin" />
            Generating Page...
          </>
        ) : (
          <>
            <FileCode className="w-4 h-4 mr-2" />
            Generate Page
          </>
        )}
      </Button>
    </div>
  )

  const renderGeneratedItems = () => (
    <div className="space-y-6">
      {/* Generated Layouts */}
      {generatedLayouts.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Layout className="w-5 h-5 mr-2" />
            Generated Layouts ({generatedLayouts.length})
          </h3>
          <div className="space-y-4">
            {generatedLayouts.map((layout) => (
              <Card key={layout.id} className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium">{layout.name}</h4>
                    <p className="text-sm text-gray-600">{layout.description}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">{layout.type}</Badge>
                    <Badge variant="outline">{layout.components.length} components</Badge>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadCode(layout)}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(layout.code)
                      toast.success('Code copied to clipboard!')
                    }}
                  >
                    <Code className="w-4 h-4 mr-1" />
                    Copy Code
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Generated Pages */}
      {generatedPages.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FileCode className="w-5 h-5 mr-2" />
            Generated Pages ({generatedPages.length})
          </h3>
          <div className="space-y-4">
            {generatedPages.map((page) => (
              <Card key={page.id} className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium">{page.name}</h4>
                    <p className="text-sm text-gray-600">{page.description}</p>
                    <p className="text-xs text-gray-500 mt-1">Route: {page.route}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">{page.type}</Badge>
                    <Badge variant="outline">{page.components.length} components</Badge>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadCode(page)}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(page.code)
                      toast.success('Code copied to clipboard!')
                    }}
                  >
                    <Code className="w-4 h-4 mr-1" />
                    Copy Code
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {generatedLayouts.length === 0 && generatedPages.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <Layers className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <div className="text-lg font-medium mb-2">No Generated Items</div>
          <div className="text-sm">
            Generate layouts and pages to see them here
          </div>
        </div>
      )}
    </div>
  )

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-2">
          <Globe className="w-5 h-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">Next.js Generator</h2>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          Generate complete Next.js layouts and pages with AI
        </p>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="border-b border-gray-200 bg-gray-50">
            <TabsList className="grid w-full grid-cols-3 h-12 bg-transparent">
              <TabsTrigger value="layout" className="flex items-center space-x-2">
                <Layout className="w-4 h-4" />
                <span>Layouts</span>
              </TabsTrigger>
              <TabsTrigger value="page" className="flex items-center space-x-2">
                <FileCode className="w-4 h-4" />
                <span>Pages</span>
              </TabsTrigger>
              <TabsTrigger value="generated" className="flex items-center space-x-2">
                <Eye className="w-4 h-4" />
                <span>Generated</span>
                {(generatedLayouts.length + generatedPages.length) > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {generatedLayouts.length + generatedPages.length}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-auto">
            <TabsContent value="layout" className="h-full p-6">
              {renderLayoutForm()}
            </TabsContent>
            
            <TabsContent value="page" className="h-full p-6">
              {renderPageForm()}
            </TabsContent>
            
            <TabsContent value="generated" className="h-full p-6">
              {renderGeneratedItems()}
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  )
}
