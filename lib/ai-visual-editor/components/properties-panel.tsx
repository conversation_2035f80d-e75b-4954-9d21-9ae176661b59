'use client'

import { useState, useMemo, useCallback } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Settings,
  Palette,
  Type,
  Zap,
  Database,
  Layout,
  RotateCcw,
  Eye,
  EyeOff,
  Search,
  Lightbulb,
  Copy,
  Save,
  History
} from 'lucide-react'
import { useSelectedComponent, useSelectedComponentProperties, useEditorStore } from '../stores/editor-store'
import { CustomFieldRenderer } from '@/lib/core/builders/components/properties-panel/custom-fields/field-renderer'
import { FieldConfig } from '@/lib/core/builders/components/properties-panel/custom-fields/types'
import { toast } from 'sonner'

const tabConfig = [
  {
    id: 'appearance',
    label: 'Appearance',
    icon: Palette,
    description: 'Colors, spacing, borders, shadows'
  },
  {
    id: 'content',
    label: 'Content',
    icon: Type,
    description: 'Text, images, links, media'
  },
  {
    id: 'behavior',
    label: 'Behavior',
    icon: Zap,
    description: 'Animations, interactions, states'
  },
  {
    id: 'data',
    label: 'Data',
    icon: Database,
    description: 'Data sources, API connections'
  },
  {
    id: 'layout',
    label: 'Layout',
    icon: Layout,
    description: 'Positioning, sizing, alignment'
  }
]

export function PropertiesPanel() {
  const selectedComponent = useSelectedComponent()
  const propertyValues = useSelectedComponentProperties()
  const { updatePropertyValue, resetPropertyValues } = useEditorStore()

  const [searchQuery, setSearchQuery] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [propertyHistory, setPropertyHistory] = useState<Record<string, any[]>>({})
  const [activeTab, setActiveTab] = useState('appearance')

  // Smart suggestions based on component analysis
  const smartSuggestions = useMemo(() => {
    if (!selectedComponent) return []

    return generateSmartSuggestions(selectedComponent, propertyValues)
  }, [selectedComponent, propertyValues])

  // Filter fields based on search
  const filteredFields = useCallback((fields: FieldConfig[]) => {
    if (!searchQuery) return fields

    return fields.filter(field =>
      field.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      field.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      field.id.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }, [searchQuery])

  if (!selectedComponent) {
    return (
      <div className="h-full flex flex-col bg-white border-r border-gray-200">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-gray-400" />
            <h2 className="text-lg font-semibold text-gray-900">Properties</h2>
          </div>
        </div>

        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <EyeOff className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <div className="text-lg font-medium mb-2">No Component Selected</div>
            <div className="text-sm">
              Select a component from the preview or generate one with AI to start editing
            </div>
          </div>
        </div>
      </div>
    )
  }

  const handleFieldChange = (fieldId: string, value: any) => {
    // Store in history for undo functionality
    setPropertyHistory(prev => ({
      ...prev,
      [fieldId]: [...(prev[fieldId] || []), propertyValues[fieldId]].slice(-10) // Keep last 10 values
    }))

    updatePropertyValue(selectedComponent.id, fieldId, value)
  }

  const handleReset = () => {
    resetPropertyValues(selectedComponent.id)
    setPropertyHistory({})
    toast.success('Properties reset to default values')
  }

  const handleCopyProperties = () => {
    navigator.clipboard.writeText(JSON.stringify(propertyValues, null, 2))
    toast.success('Properties copied to clipboard')
  }

  const handleSaveAsPreset = () => {
    // This would save the current property values as a preset
    const presetName = prompt('Enter preset name:')
    if (presetName) {
      // Save to local storage or API
      localStorage.setItem(`preset_${presetName}`, JSON.stringify(propertyValues))
      toast.success(`Preset "${presetName}" saved`)
    }
  }

  const applySuggestion = (suggestion: SmartSuggestion) => {
    Object.entries(suggestion.properties).forEach(([fieldId, value]) => {
      handleFieldChange(fieldId, value)
    })
    toast.success(`Applied suggestion: ${suggestion.title}`)
  }

  const renderFieldSection = (sectionId: string, fields: FieldConfig[]) => {
    const visibleFields = filteredFields(fields)

    if (!fields || fields.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          <div className="text-sm">No {sectionId} properties available</div>
        </div>
      )
    }

    if (searchQuery && visibleFields.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          <div className="text-sm">No properties match "{searchQuery}"</div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSearchQuery('')}
            className="mt-2 text-xs"
          >
            Clear search
          </Button>
        </div>
      )
    }

    return (
      <div className="space-y-4 p-4">
        {visibleFields.map((field) => (
          <div key={field.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <CustomFieldRenderer
                  config={field}
                  value={propertyValues[field.id]}
                  onChange={(value) => handleFieldChange(field.id, value)}
                  onValidate={(isValid, message) => {
                    if (!isValid && message) {
                      toast.error(`${field.label}: ${message}`)
                    }
                  }}
                  className="w-full"
                />
              </div>
              {propertyHistory[field.id] && propertyHistory[field.id].length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const lastValue = propertyHistory[field.id].pop()
                    if (lastValue !== undefined) {
                      updatePropertyValue(selectedComponent.id, field.id, lastValue)
                      setPropertyHistory(prev => ({
                        ...prev,
                        [field.id]: prev[field.id] || []
                      }))
                    }
                  }}
                  className="ml-2 h-6 w-6 p-0"
                  title="Undo last change"
                >
                  <History className="w-3 h-3" />
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white border-r border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">Properties</h2>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className="text-xs"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            Reset
          </Button>
        </div>
        
        {/* Component Info */}
        <div className="mt-3">
          <div className="flex items-center space-x-2">
            <Eye className="w-4 h-4 text-gray-500" />
            <span className="font-medium text-gray-900">{selectedComponent.name}</span>
            <Badge variant="secondary" className="text-xs">
              {selectedComponent.category}
            </Badge>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            {selectedComponent.description}
          </p>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-2 mb-3">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search properties..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 h-8 text-sm"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSuggestions(!showSuggestions)}
            className={showSuggestions ? 'bg-blue-50 border-blue-200' : ''}
          >
            <Lightbulb className="w-4 h-4" />
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleCopyProperties}>
            <Copy className="w-3 h-3 mr-1" />
            Copy
          </Button>
          <Button variant="outline" size="sm" onClick={handleSaveAsPreset}>
            <Save className="w-3 h-3 mr-1" />
            Save Preset
          </Button>
          <Button variant="outline" size="sm" onClick={handleReset}>
            <RotateCcw className="w-3 h-3 mr-1" />
            Reset
          </Button>
        </div>
      </div>

      {/* Smart Suggestions */}
      {showSuggestions && smartSuggestions.length > 0 && (
        <div className="p-3 border-b border-gray-200 bg-blue-50">
          <div className="flex items-center space-x-2 mb-2">
            <Lightbulb className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">Smart Suggestions</span>
          </div>
          <div className="space-y-2">
            {smartSuggestions.slice(0, 3).map((suggestion, index) => (
              <Card key={index} className="p-2 bg-white border-blue-200">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-xs font-medium text-gray-900">{suggestion.title}</div>
                    <div className="text-xs text-gray-600">{suggestion.description}</div>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => applySuggestion(suggestion)}
                    className="text-xs h-6"
                  >
                    Apply
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Properties Tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="border-b border-gray-200 bg-gray-50">
            <TabsList className="grid w-full grid-cols-5 h-auto p-1">
              {tabConfig.map((tab) => {
                const Icon = tab.icon
                const allFields = selectedComponent.propertiesConfig[tab.id as keyof typeof selectedComponent.propertiesConfig] || []
                const visibleFields = filteredFields(allFields)
                const fieldCount = searchQuery ? visibleFields.length : allFields.length

                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex flex-col items-center p-2 text-xs data-[state=active]:bg-white"
                  >
                    <Icon className="w-4 h-4 mb-1" />
                    <span className="font-medium">{tab.label}</span>
                    {fieldCount > 0 && (
                      <Badge variant="secondary" className="text-[10px] px-1 py-0 mt-1">
                        {fieldCount}
                      </Badge>
                    )}
                    {searchQuery && visibleFields.length !== allFields.length && (
                      <Badge variant="outline" className="text-[10px] px-1 py-0 mt-1 border-orange-200 text-orange-600">
                        {visibleFields.length}/{allFields.length}
                      </Badge>
                    )}
                  </TabsTrigger>
                )
              })}
            </TabsList>
          </div>

          <div className="flex-1 overflow-hidden">
            {tabConfig.map((tab) => (
              <TabsContent
                key={tab.id}
                value={tab.id}
                className="h-full mt-0 data-[state=active]:flex data-[state=active]:flex-col"
              >
                <div className="p-3 border-b border-gray-100 bg-gray-50">
                  <div className="flex items-center space-x-2">
                    <tab.icon className="w-4 h-4 text-gray-600" />
                    <span className="font-medium text-gray-900">{tab.label}</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">{tab.description}</p>
                </div>
                
                <ScrollArea className="flex-1">
                  {renderFieldSection(
                    tab.id,
                    selectedComponent.propertiesConfig[tab.id as keyof typeof selectedComponent.propertiesConfig] || []
                  )}
                </ScrollArea>
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          {Object.values(selectedComponent.propertiesConfig).flat().length} properties available
        </div>
      </div>
    </div>
  )
}

// Smart suggestions interface and generator
interface SmartSuggestion {
  title: string
  description: string
  properties: Record<string, any>
  category: string
  confidence: number
}

function generateSmartSuggestions(
  component: any,
  currentProperties: Record<string, any>
): SmartSuggestion[] {
  const suggestions: SmartSuggestion[] = []

  // Color harmony suggestions
  if (currentProperties.backgroundColor && !currentProperties.textColor) {
    const bgColor = currentProperties.backgroundColor
    const suggestedTextColor = getContrastingColor(bgColor)

    suggestions.push({
      title: 'Improve Text Contrast',
      description: 'Add contrasting text color for better readability',
      properties: { textColor: suggestedTextColor },
      category: 'accessibility',
      confidence: 0.9
    })
  }

  // Spacing consistency
  if (currentProperties.padding && !currentProperties.margin) {
    suggestions.push({
      title: 'Add Consistent Spacing',
      description: 'Add margin to match your padding for visual balance',
      properties: {
        margin: {
          top: Math.floor(currentProperties.padding.top / 2),
          right: Math.floor(currentProperties.padding.right / 2),
          bottom: Math.floor(currentProperties.padding.bottom / 2),
          left: Math.floor(currentProperties.padding.left / 2)
        }
      },
      category: 'design',
      confidence: 0.7
    })
  }

  // Shadow and border radius pairing
  if (currentProperties.borderRadius > 8 && !currentProperties.shadow) {
    suggestions.push({
      title: 'Add Subtle Shadow',
      description: 'Rounded corners look great with a soft shadow',
      properties: { shadow: '0 2px 8px rgba(0,0,0,0.1)' },
      category: 'design',
      confidence: 0.8
    })
  }

  // Animation suggestions for interactive elements
  if (component.category === 'button' && !currentProperties.hoverEffect) {
    suggestions.push({
      title: 'Add Hover Effect',
      description: 'Make buttons more interactive with hover effects',
      properties: { hoverEffect: 'lift' },
      category: 'interaction',
      confidence: 0.85
    })
  }

  // Responsive suggestions
  if (currentProperties.width === 'w-full' && component.category === 'card') {
    suggestions.push({
      title: 'Optimize for Mobile',
      description: 'Consider fixed width for better mobile experience',
      properties: { width: 'w-[350px]' },
      category: 'responsive',
      confidence: 0.6
    })
  }

  return suggestions.sort((a, b) => b.confidence - a.confidence)
}

function getContrastingColor(backgroundColor: string): string {
  // Simple contrast calculation - in production, use a proper color library
  if (backgroundColor.toLowerCase().includes('dark') || backgroundColor === '#000000') {
    return '#ffffff'
  }
  if (backgroundColor.toLowerCase().includes('light') || backgroundColor === '#ffffff') {
    return '#000000'
  }

  // For hex colors, calculate luminance
  if (backgroundColor.startsWith('#')) {
    const hex = backgroundColor.slice(1)
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

    return luminance > 0.5 ? '#000000' : '#ffffff'
  }

  return '#000000' // Default fallback
}
