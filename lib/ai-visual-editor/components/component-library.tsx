'use client'

import { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Filter, 
  Star, 
  Download, 
  Eye, 
  Copy,
  Heart,
  Zap,
  Sparkles,
  Grid,
  List
} from 'lucide-react'
import { useEditorStore } from '../stores/editor-store'
import { ComponentTemplate } from '../services/component-persistence'
import { toast } from 'sonner'

interface ComponentLibraryProps {
  onComponentSelect?: (template: ComponentTemplate) => void
  showCategories?: boolean
  showSearch?: boolean
  viewMode?: 'grid' | 'list'
}

const TEMPLATE_CATEGORIES = [
  { id: 'all', label: 'All Templates', icon: Grid },
  { id: 'hero', label: 'Hero Sections', icon: Zap },
  { id: 'card', label: 'Cards', icon: Grid },
  { id: 'button', label: 'Buttons', icon: Sparkles },
  { id: 'form', label: 'Forms', icon: List },
  { id: 'navigation', label: 'Navigation', icon: List },
  { id: 'layout', label: 'Layouts', icon: Grid },
  { id: 'popular', label: 'Popular', icon: Star },
  { id: 'recent', label: 'Recent', icon: Sparkles }
]

const MOCK_TEMPLATES: ComponentTemplate[] = [
  {
    id: 'hero-1',
    name: 'Modern Hero Section',
    description: 'Clean hero section with gradient background and call-to-action',
    category: 'hero',
    jsx: `<div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
      <div className="container mx-auto text-center">
        <h1 className="text-5xl font-bold mb-6">Welcome to the Future</h1>
        <p className="text-xl mb-8">Build amazing experiences with AI-powered components</p>
        <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100">
          Get Started
        </button>
      </div>
    </div>`,
    propertiesConfig: {},
    defaultValues: {
      title: 'Welcome to the Future',
      description: 'Build amazing experiences with AI-powered components',
      buttonText: 'Get Started'
    },
    tags: ['gradient', 'modern', 'cta'],
    isPublic: true,
    createdBy: 'system',
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 245,
    rating: 4.8,
    version: '1.0.0'
  },
  {
    id: 'card-1',
    name: 'Product Card',
    description: 'Elegant product card with image, title, and pricing',
    category: 'card',
    jsx: `<div className="bg-white rounded-lg shadow-lg overflow-hidden">
      <img src="/placeholder.jpg" alt="Product" className="w-full h-48 object-cover" />
      <div className="p-6">
        <h3 className="text-xl font-semibold mb-2">Product Name</h3>
        <p className="text-gray-600 mb-4">Product description goes here</p>
        <div className="flex justify-between items-center">
          <span className="text-2xl font-bold text-blue-600">$99</span>
          <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Add to Cart
          </button>
        </div>
      </div>
    </div>`,
    propertiesConfig: {},
    defaultValues: {
      title: 'Product Name',
      description: 'Product description goes here',
      price: '$99',
      image: '/placeholder.jpg'
    },
    tags: ['ecommerce', 'product', 'card'],
    isPublic: true,
    createdBy: 'system',
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 189,
    rating: 4.6,
    version: '1.0.0'
  }
]

export function ComponentLibrary({ 
  onComponentSelect, 
  showCategories = true,
  showSearch = true,
  viewMode = 'grid'
}: ComponentLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState<'popular' | 'recent' | 'rating'>('popular')
  const [currentViewMode, setCurrentViewMode] = useState(viewMode)
  const { addComponent } = useEditorStore()

  // Filter and sort templates
  const filteredTemplates = useMemo(() => {
    let filtered = MOCK_TEMPLATES

    // Filter by category
    if (selectedCategory !== 'all') {
      if (selectedCategory === 'popular') {
        filtered = filtered.filter(t => t.usageCount > 100)
      } else if (selectedCategory === 'recent') {
        filtered = filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()).slice(0, 10)
      } else {
        filtered = filtered.filter(t => t.category === selectedCategory)
      }
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Sort templates
    switch (sortBy) {
      case 'popular':
        filtered.sort((a, b) => b.usageCount - a.usageCount)
        break
      case 'recent':
        filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        break
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating)
        break
    }

    return filtered
  }, [searchQuery, selectedCategory, sortBy])

  const handleUseTemplate = (template: ComponentTemplate) => {
    const component = {
      id: `template_${template.id}_${Date.now()}`,
      name: template.name,
      description: template.description,
      category: template.category as any,
      jsx: template.jsx,
      props: {},
      propertiesConfig: template.propertiesConfig,
      defaultValues: template.defaultValues,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    addComponent(component)
    onComponentSelect?.(template)
    toast.success(`Added ${template.name} to your editor`)
  }

  const handlePreviewTemplate = (template: ComponentTemplate) => {
    // Open preview modal or navigate to preview
    toast.info(`Preview for ${template.name} (feature coming soon)`)
  }

  const renderTemplateCard = (template: ComponentTemplate) => {
    if (currentViewMode === 'list') {
      return (
        <Card key={template.id} className="p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
              <Grid className="w-8 h-8 text-gray-400" />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-semibold text-gray-900">{template.name}</h3>
                <Badge variant="secondary">{template.category}</Badge>
                <div className="flex items-center space-x-1">
                  <Star className="w-3 h-3 text-yellow-500 fill-current" />
                  <span className="text-xs text-gray-600">{template.rating}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-2">{template.description}</p>
              <div className="flex items-center space-x-2">
                {template.tags.slice(0, 3).map(tag => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                <span className="text-xs text-gray-500">
                  {template.usageCount} uses
                </span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePreviewTemplate(template)}
              >
                <Eye className="w-4 h-4 mr-1" />
                Preview
              </Button>
              <Button
                size="sm"
                onClick={() => handleUseTemplate(template)}
              >
                <Download className="w-4 h-4 mr-1" />
                Use
              </Button>
            </div>
          </div>
        </Card>
      )
    }

    return (
      <Card key={template.id} className="overflow-hidden hover:shadow-lg transition-shadow">
        <div className="aspect-video bg-gray-100 flex items-center justify-center">
          <Grid className="w-12 h-12 text-gray-400" />
        </div>
        
        <div className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-900 truncate">{template.name}</h3>
            <div className="flex items-center space-x-1">
              <Star className="w-3 h-3 text-yellow-500 fill-current" />
              <span className="text-xs text-gray-600">{template.rating}</span>
            </div>
          </div>
          
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">{template.description}</p>
          
          <div className="flex items-center justify-between mb-3">
            <Badge variant="secondary">{template.category}</Badge>
            <span className="text-xs text-gray-500">{template.usageCount} uses</span>
          </div>
          
          <div className="flex items-center space-x-1 mb-3">
            {template.tags.slice(0, 2).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePreviewTemplate(template)}
              className="flex-1"
            >
              <Eye className="w-4 h-4 mr-1" />
              Preview
            </Button>
            <Button
              size="sm"
              onClick={() => handleUseTemplate(template)}
              className="flex-1"
            >
              <Download className="w-4 h-4 mr-1" />
              Use
            </Button>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5 text-purple-600" />
            <h2 className="text-lg font-semibold text-gray-900">Component Library</h2>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={currentViewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCurrentViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={currentViewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCurrentViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        {showSearch && (
          <div className="flex items-center space-x-2 mb-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="popular">Most Popular</option>
              <option value="recent">Most Recent</option>
              <option value="rating">Highest Rated</option>
            </select>
          </div>
        )}

        <div className="text-sm text-gray-600">
          {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''} found
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Categories Sidebar */}
        {showCategories && (
          <div className="w-48 border-r border-gray-200 bg-gray-50">
            <ScrollArea className="h-full p-2">
              <div className="space-y-1">
                {TEMPLATE_CATEGORIES.map(category => {
                  const Icon = category.icon
                  return (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setSelectedCategory(category.id)}
                      className="w-full justify-start"
                    >
                      <Icon className="w-4 h-4 mr-2" />
                      {category.label}
                    </Button>
                  )
                })}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Templates Grid/List */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-4">
              {filteredTemplates.length === 0 ? (
                <div className="text-center py-12">
                  <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <div className="text-lg font-medium text-gray-900 mb-2">No templates found</div>
                  <div className="text-sm text-gray-600">
                    Try adjusting your search or category filters
                  </div>
                </div>
              ) : (
                <div className={
                  currentViewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
                    : 'space-y-3'
                }>
                  {filteredTemplates.map(renderTemplateCard)}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  )
}
