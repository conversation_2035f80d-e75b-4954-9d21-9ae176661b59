'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from '@/components/ui/tabs'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  MessageSquare, 
  Settings, 
  Eye, 
  Layers,
  Sparkles,
  RotateCcw,
  Download,
  Share
} from 'lucide-react'
import { AiChatPanel } from './ai-chat-panel'
import { PropertiesPanel } from './properties-panel'
import { LivePreview } from './live-preview'
import { ComponentTree } from './component-tree'
import { useEditorStore } from '../stores/editor-store'
import { toast } from 'sonner'

export function AIVisualEditor() {
  const { 
    components, 
    selectedComponentId, 
    isGenerating, 
    isAIResponding,
    clearChat,
    showComponentTree,
    toggleComponentTree
  } = useEditorStore()

  const selectedComponent = components.find(c => c.id === selectedComponentId)

  const handleExportComponents = () => {
    if (components.length === 0) {
      toast.error('No components to export')
      return
    }

    const exportData = {
      components: components.map(comp => ({
        name: comp.name,
        description: comp.description,
        category: comp.category,
        jsx: comp.jsx,
        defaultValues: comp.defaultValues
      })),
      exportedAt: new Date().toISOString(),
      version: '1.0.0'
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ai-components-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success('Components exported successfully!')
  }

  const handleShareComponents = async () => {
    if (components.length === 0) {
      toast.error('No components to share')
      return
    }

    try {
      const shareData = {
        title: 'AI Generated Components',
        text: `Check out these ${components.length} AI-generated components!`,
        url: window.location.href
      }

      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        await navigator.clipboard.writeText(window.location.href)
        toast.success('Link copied to clipboard!')
      }
    } catch (error) {
      console.error('Error sharing:', error)
      toast.error('Failed to share components')
    }
  }

  const handleResetEditor = () => {
    if (components.length === 0) {
      toast.info('Editor is already empty')
      return
    }

    if (confirm('This will delete all components and clear the chat. Are you sure?')) {
      // This would need to be implemented in the store
      clearChat()
      toast.success('Editor reset successfully')
    }
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Top Toolbar */}
      <div className="h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4 shadow-sm">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Sparkles className="w-6 h-6 text-blue-600" />
            <h1 className="text-xl font-bold text-gray-900">AI Visual Editor</h1>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Badge variant="secondary">
              {components.length} component{components.length !== 1 ? 's' : ''}
            </Badge>
            {selectedComponent && (
              <>
                <span>•</span>
                <span>Editing: {selectedComponent.name}</span>
              </>
            )}
            {(isGenerating || isAIResponding) && (
              <>
                <span>•</span>
                <Badge variant="outline" className="text-blue-600 border-blue-200">
                  AI Working...
                </Badge>
              </>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleComponentTree}
            className={showComponentTree ? 'bg-blue-50 border-blue-200' : ''}
          >
            <Layers className="w-4 h-4 mr-1" />
            Components
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportComponents}
            disabled={components.length === 0}
          >
            <Download className="w-4 h-4 mr-1" />
            Export
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleShareComponents}
            disabled={components.length === 0}
          >
            <Share className="w-4 h-4 mr-1" />
            Share
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetEditor}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Reset
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - AI Chat */}
        <div className="w-80 flex-shrink-0">
          <AiChatPanel />
        </div>

        {/* Center Panel - Properties or Component Tree */}
        <div className="w-96 flex-shrink-0 border-r border-gray-200">
          <Tabs defaultValue="properties" className="h-full">
            <TabsList className="w-full grid grid-cols-2 h-12">
              <TabsTrigger value="properties" className="flex items-center space-x-2">
                <Settings className="w-4 h-4" />
                <span>Properties</span>
              </TabsTrigger>
              <TabsTrigger value="components" className="flex items-center space-x-2">
                <Layers className="w-4 h-4" />
                <span>Components</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="properties" className="h-full mt-0">
              <PropertiesPanel />
            </TabsContent>
            
            <TabsContent value="components" className="h-full mt-0">
              <ComponentTree />
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Panel - Live Preview */}
        <div className="flex-1">
          <LivePreview />
        </div>
      </div>

      {/* Status Bar */}
      <div className="h-8 bg-gray-100 border-t border-gray-200 flex items-center justify-between px-4 text-xs text-gray-600">
        <div className="flex items-center space-x-4">
          <span>
            {components.length > 0 
              ? `${components.length} component${components.length !== 1 ? 's' : ''} generated`
              : 'Ready to generate components'
            }
          </span>
          {selectedComponent && (
            <>
              <span>•</span>
              <span>
                {Object.values(selectedComponent.propertiesConfig).flat().length} properties available
              </span>
            </>
          )}
        </div>
        
        <div className="flex items-center space-x-4">
          <span>AI Visual Editor v1.0.0</span>
          <span>•</span>
          <span>Last updated: {new Date().toLocaleTimeString()}</span>
        </div>
      </div>
    </div>
  )
}
