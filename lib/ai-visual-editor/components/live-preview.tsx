'use client'

import { useState, useMemo, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  Eye, 
  Code, 
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { useEditorStore, useSelectedComponent, useSelectedComponentProperties } from '../stores/editor-store'
import { DynamicComponentRenderer } from './dynamic-component-renderer'

const devicePresets = {
  desktop: { width: '100%', height: '100%', icon: Monitor, label: 'Desktop' },
  tablet: { width: '768px', height: '1024px', icon: Tablet, label: 'Tablet' },
  mobile: { width: '375px', height: '667px', icon: Smartphone, label: 'Mobile' }
}

export function LivePreview() {
  const { previewMode, setPreviewMode, components, selectedComponentId, selectComponent } = useEditorStore()
  const selectedComponent = useSelectedComponent()
  const selectedProperties = useSelectedComponentProperties()
  const [showCode, setShowCode] = useState(false)
  const [renderError, setRenderError] = useState<string | null>(null)

  const currentDevice = devicePresets[previewMode]

  const handleDeviceChange = (device: keyof typeof devicePresets) => {
    setPreviewMode(device)
  }

  const handleRefresh = useCallback(() => {
    setRenderError(null)
    // Force re-render by updating a key or similar mechanism
  }, [])

  const previewContent = useMemo(() => {
    if (components.length === 0) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <Eye className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <div className="text-xl font-medium mb-2">No Components Yet</div>
            <div className="text-sm">
              Generate components with AI to see them here
            </div>
          </div>
        </div>
      )
    }

    if (selectedComponent) {
      // Show only selected component
      return (
        <div className="p-6">
          <div className="mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <h3 className="font-medium text-gray-900">{selectedComponent.name}</h3>
                <Badge variant="secondary">{selectedComponent.category}</Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCode(!showCode)}
                >
                  <Code className="w-4 h-4 mr-1" />
                  {showCode ? 'Preview' : 'Code'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <p className="text-sm text-gray-600">{selectedComponent.description}</p>
          </div>

          {showCode ? (
            <Card className="p-4 bg-gray-900 text-gray-100 overflow-auto">
              <pre className="text-sm">
                <code>{selectedComponent.jsx}</code>
              </pre>
            </Card>
          ) : (
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              {renderError ? (
                <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center space-x-2 text-red-800 mb-2">
                    <AlertTriangle className="w-5 h-5" />
                    <span className="font-medium">Render Error</span>
                  </div>
                  <p className="text-sm text-red-700">{renderError}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    className="mt-3"
                  >
                    Try Again
                  </Button>
                </div>
              ) : (
                <DynamicComponentRenderer
                  component={selectedComponent}
                  properties={selectedProperties}
                  onError={setRenderError}
                />
              )}
            </div>
          )}
        </div>
      )
    }

    // Show all components
    return (
      <div className="p-6 space-y-6">
        <div className="text-center mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">All Components</h3>
          <p className="text-sm text-gray-600">Click on a component to select and edit it</p>
        </div>
        
        {components.map((component) => (
          <Card
            key={component.id}
            className={`p-4 cursor-pointer transition-all hover:shadow-md ${
              selectedComponentId === component.id 
                ? 'ring-2 ring-blue-500 border-blue-200' 
                : 'border-gray-200'
            }`}
            onClick={() => selectComponent(component.id)}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <h4 className="font-medium text-gray-900">{component.name}</h4>
                <Badge variant="secondary">{component.category}</Badge>
              </div>
              {selectedComponentId === component.id && (
                <CheckCircle className="w-5 h-5 text-blue-600" />
              )}
            </div>
            
            <div className="border border-gray-200 rounded overflow-hidden">
              <DynamicComponentRenderer
                component={component}
                properties={selectedComponentId === component.id ? selectedProperties : component.defaultValues}
                onError={(error) => console.warn(`Render error for ${component.name}:`, error)}
              />
            </div>
          </Card>
        ))}
      </div>
    )
  }, [components, selectedComponent, selectedProperties, selectedComponentId, showCode, renderError])

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Eye className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Live Preview</h2>
          </div>
          
          {/* Device Selector */}
          <div className="flex items-center space-x-1 bg-white border border-gray-200 rounded-lg p-1">
            {Object.entries(devicePresets).map(([device, config]) => {
              const Icon = config.icon
              return (
                <Button
                  key={device}
                  variant={previewMode === device ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleDeviceChange(device as keyof typeof devicePresets)}
                  className="h-8 px-3"
                >
                  <Icon className="w-4 h-4 mr-1" />
                  {config.label}
                </Button>
              )
            })}
          </div>
        </div>
        
        {/* Stats */}
        <div className="mt-2 flex items-center space-x-4 text-sm text-gray-600">
          <span>{components.length} component{components.length !== 1 ? 's' : ''}</span>
          {selectedComponent && (
            <>
              <span>•</span>
              <span>Editing: {selectedComponent.name}</span>
            </>
          )}
          <span>•</span>
          <span>{currentDevice.label} view</span>
        </div>
      </div>

      {/* Preview Area */}
      <div className="flex-1 overflow-auto bg-gray-100">
        <div className="h-full flex items-center justify-center p-4">
          <div
            className="bg-white shadow-lg rounded-lg overflow-hidden transition-all duration-300"
            style={{
              width: currentDevice.width,
              height: currentDevice.height,
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          >
            <div className="h-full overflow-auto">
              {previewContent}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            {previewMode === 'desktop' ? 'Responsive' : `${currentDevice.width} × ${currentDevice.height}`}
          </span>
          <span>
            Last updated: {new Date().toLocaleTimeString()}
          </span>
        </div>
      </div>
    </div>
  )
}
