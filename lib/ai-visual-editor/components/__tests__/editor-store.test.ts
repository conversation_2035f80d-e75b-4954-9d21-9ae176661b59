import { useEditorStore } from '../stores/editor-store'
import { GeneratedComponent } from '../types'

// Mock component for testing
const mockComponent: GeneratedComponent = {
  id: 'test-component-1',
  name: 'Test Hero Component',
  description: 'A test hero component for testing',
  category: 'layout',
  jsx: '<div className="hero">Test Hero</div>',
  props: {},
  propertiesConfig: {
    appearance: [],
    content: [],
    behavior: [],
    data: [],
    layout: []
  },
  defaultValues: {
    title: 'Test Title',
    backgroundColor: '#ffffff'
  },
  createdAt: new Date(),
  updatedAt: new Date()
}

describe('Editor Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    useEditorStore.setState({
      components: [],
      selectedComponentId: null,
      propertyValues: {},
      isGenerating: false,
      previewMode: 'desktop',
      showComponentTree: true,
      chatMessages: [],
      isAIResponding: false
    })
  })

  test('should add component to store', () => {
    const { addComponent } = useEditorStore.getState()
    
    addComponent(mockComponent)
    
    const { components, selectedComponentId, propertyValues } = useEditorStore.getState()
    
    expect(components).toHaveLength(1)
    expect(components[0]).toEqual(mockComponent)
    expect(selectedComponentId).toBe(mockComponent.id)
    expect(propertyValues[mockComponent.id]).toEqual(mockComponent.defaultValues)
  })

  test('should update component properties', () => {
    const { addComponent, updatePropertyValue } = useEditorStore.getState()
    
    addComponent(mockComponent)
    updatePropertyValue(mockComponent.id, 'title', 'Updated Title')
    
    const { propertyValues } = useEditorStore.getState()
    
    expect(propertyValues[mockComponent.id].title).toBe('Updated Title')
    expect(propertyValues[mockComponent.id].backgroundColor).toBe('#ffffff')
  })

  test('should select component', () => {
    const { addComponent, selectComponent } = useEditorStore.getState()
    
    addComponent(mockComponent)
    selectComponent(mockComponent.id)
    
    const { selectedComponentId } = useEditorStore.getState()
    
    expect(selectedComponentId).toBe(mockComponent.id)
  })

  test('should delete component', () => {
    const { addComponent, deleteComponent } = useEditorStore.getState()
    
    addComponent(mockComponent)
    deleteComponent(mockComponent.id)
    
    const { components, selectedComponentId, propertyValues } = useEditorStore.getState()
    
    expect(components).toHaveLength(0)
    expect(selectedComponentId).toBeNull()
    expect(propertyValues[mockComponent.id]).toBeUndefined()
  })

  test('should reset property values', () => {
    const { addComponent, updatePropertyValue, resetPropertyValues } = useEditorStore.getState()
    
    addComponent(mockComponent)
    updatePropertyValue(mockComponent.id, 'title', 'Modified Title')
    resetPropertyValues(mockComponent.id)
    
    const { propertyValues } = useEditorStore.getState()
    
    expect(propertyValues[mockComponent.id]).toEqual(mockComponent.defaultValues)
  })

  test('should toggle preview mode', () => {
    const { setPreviewMode } = useEditorStore.getState()
    
    setPreviewMode('tablet')
    
    const { previewMode } = useEditorStore.getState()
    
    expect(previewMode).toBe('tablet')
  })

  test('should manage generation state', () => {
    const { setGenerating } = useEditorStore.getState()
    
    setGenerating(true)
    
    const { isGenerating } = useEditorStore.getState()
    
    expect(isGenerating).toBe(true)
  })
})

// Test selectors
describe('Editor Store Selectors', () => {
  beforeEach(() => {
    useEditorStore.setState({
      components: [mockComponent],
      selectedComponentId: mockComponent.id,
      propertyValues: {
        [mockComponent.id]: mockComponent.defaultValues
      }
    })
  })

  test('useSelectedComponent should return selected component', () => {
    const selectedComponent = useEditorStore((state) => {
      const selectedId = state.selectedComponentId
      return selectedId ? state.components.find(c => c.id === selectedId) : null
    })
    
    expect(selectedComponent).toEqual(mockComponent)
  })

  test('useSelectedComponentProperties should return selected component properties', () => {
    const selectedProperties = useEditorStore((state) => {
      const selectedId = state.selectedComponentId
      return selectedId ? state.propertyValues[selectedId] || {} : {}
    })
    
    expect(selectedProperties).toEqual(mockComponent.defaultValues)
  })
})
