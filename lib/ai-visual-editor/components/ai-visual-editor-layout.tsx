'use client'

import { EditorLayout } from '@/components/admin/editor-layout'
import { AiChatPanel } from './ai-chat-panel'
import { PropertiesPanel } from './properties-panel'
import { LivePreview } from './live-preview'
import { ComponentTree } from './component-tree'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  Layers,
  MessageSquare,
  Sparkles,
  Download,
  Share,
  RotateCcw,
  Globe,
  FileCode
} from 'lucide-react'
import { NextJSLayoutGenerator } from './nextjs-layout-generator'
import { useEditorStore } from '../stores/editor-store'
import { toast } from 'sonner'

export function AIVisualEditorLayout() {
  const { 
    components, 
    selectedComponentId, 
    isGenerating, 
    isAIResponding,
    clearChat
  } = useEditorStore()

  const selectedComponent = components.find(c => c.id === selectedComponentId)

  const handleExportComponents = () => {
    if (components.length === 0) {
      toast.error('No components to export')
      return
    }

    const exportData = {
      components: components.map(comp => ({
        name: comp.name,
        description: comp.description,
        category: comp.category,
        jsx: comp.jsx,
        defaultValues: comp.defaultValues
      })),
      exportedAt: new Date().toISOString(),
      version: '1.0.0'
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ai-components-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success('Components exported successfully!')
  }

  const handleShareComponents = async () => {
    if (components.length === 0) {
      toast.error('No components to share')
      return
    }

    try {
      const shareData = {
        title: 'AI Generated Components',
        text: `Check out these ${components.length} AI-generated components!`,
        url: window.location.href
      }

      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        await navigator.clipboard.writeText(window.location.href)
        toast.success('Link copied to clipboard!')
      }
    } catch (error) {
      console.error('Error sharing:', error)
      toast.error('Failed to share components')
    }
  }

  const handleResetEditor = () => {
    if (components.length === 0) {
      toast.info('Editor is already empty')
      return
    }

    if (confirm('This will delete all components and clear the chat. Are you sure?')) {
      clearChat()
      toast.success('Editor reset successfully')
    }
  }

  // Left Panel - AI Chat
  const leftPanel = (
    <div className="h-full bg-white">
      <div className="p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-gray-900">AI Assistant</h3>
          {(isGenerating || isAIResponding) && (
            <Badge variant="outline" className="text-blue-600 border-blue-200 text-xs">
              Working...
            </Badge>
          )}
        </div>
      </div>
      <div className="h-[calc(100%-60px)]">
        <AiChatPanel />
      </div>
    </div>
  )

  // Right Panel - Properties and Components
  const rightPanel = (
    <div className="h-full bg-white">
      <Tabs defaultValue="properties" className="h-full">
        <div className="border-b border-gray-200 bg-gray-50">
          <TabsList className="w-full grid grid-cols-3 h-12 bg-transparent">
            <TabsTrigger
              value="properties"
              className="flex items-center space-x-2 data-[state=active]:bg-white"
            >
              <Settings className="w-4 h-4" />
              <span>Properties</span>
            </TabsTrigger>
            <TabsTrigger
              value="components"
              className="flex items-center space-x-2 data-[state=active]:bg-white"
            >
              <Layers className="w-4 h-4" />
              <span>Components</span>
              {components.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {components.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="nextjs"
              className="flex items-center space-x-2 data-[state=active]:bg-white"
            >
              <Globe className="w-4 h-4" />
              <span>Next.js</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="properties" className="h-[calc(100%-60px)] mt-0">
          <PropertiesPanel />
        </TabsContent>

        <TabsContent value="components" className="h-[calc(100%-60px)] mt-0">
          <ComponentTree />
        </TabsContent>

        <TabsContent value="nextjs" className="h-[calc(100%-60px)] mt-0">
          <NextJSLayoutGenerator />
        </TabsContent>
      </Tabs>
    </div>
  )

  // Main Content - Live Preview with Header
  const mainContent = (
    <div className="h-full flex flex-col bg-white">
      {/* Preview Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Sparkles className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">AI Visual Editor</h3>
            </div>
            
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Badge variant="secondary">
                {components.length} component{components.length !== 1 ? 's' : ''}
              </Badge>
              {selectedComponent && (
                <>
                  <span>•</span>
                  <span>Editing: {selectedComponent.name}</span>
                </>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportComponents}
              disabled={components.length === 0}
            >
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleShareComponents}
              disabled={components.length === 0}
            >
              <Share className="w-4 h-4 mr-1" />
              Share
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleResetEditor}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <RotateCcw className="w-4 h-4 mr-1" />
              Reset
            </Button>
          </div>
        </div>
      </div>

      {/* Live Preview */}
      <div className="flex-1 overflow-hidden">
        <LivePreview />
      </div>
    </div>
  )

  return (
    <EditorLayout
      leftPanel={leftPanel}
      rightPanel={rightPanel}
      className="bg-gray-50"
    >
      {mainContent}
    </EditorLayout>
  )
}
