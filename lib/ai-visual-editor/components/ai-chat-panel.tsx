'use client'

import { useChat } from '@ai-sdk/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Copy, Play, Loader2, MessageSquare, Sparkles } from 'lucide-react'
import { useEditorStore } from '../stores/editor-store'
import { GeneratedComponent } from '../types'
import { toast } from 'sonner'

export function AiChatPanel() {
  const { addComponent, setGenerating, setAIResponding } = useEditorStore()
  
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: '/api/ai-visual-editor/chat',
    maxSteps: 5,
    
    onResponse: () => {
      setAIResponding(true)
    },
    
    onFinish: () => {
      setAIResponding(false)
      setGenerating(false)
    },
    
    onError: (error) => {
      console.error('Chat error:', error)
      setAIResponding(false)
      setGenerating(false)
      toast.error('Failed to communicate with AI. Please try again.')
    }
  })

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return
    
    setGenerating(true)
    handleSubmit(e)
  }

  const handleComponentGenerated = (component: GeneratedComponent) => {
    addComponent(component)
    toast.success(`Generated ${component.name} component successfully!`)
  }

  const renderMessage = (message: any) => {
    return (
      <div key={message.id} className="mb-4">
        <div className={`p-3 rounded-lg ${
          message.role === 'user' 
            ? 'bg-blue-50 border border-blue-200 ml-8' 
            : 'bg-gray-50 border border-gray-200 mr-8'
        }`}>
          {message.parts?.map((part: any, index: number) => {
            switch (part.type) {
              case 'text':
                return (
                  <div key={index} className="text-sm text-gray-900 whitespace-pre-wrap">
                    {part.text}
                  </div>
                )
                
              case 'tool-invocation':
                return (
                  <div key={index} className="mt-2">
                    {renderToolInvocation(part.toolInvocation)}
                  </div>
                )
                
              default:
                return null
            }
          }) || (
            <div className="text-sm text-gray-900 whitespace-pre-wrap">
              {message.content}
            </div>
          )}
        </div>
      </div>
    )
  }

  const renderToolInvocation = (toolInvocation: any) => {
    switch (toolInvocation.toolName) {
      case 'generateComponent':
        if (toolInvocation.state === 'result') {
          const result = toolInvocation.result
          if (result.success && result.component) {
            return (
              <Card className="p-4 bg-green-50 border-green-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Sparkles className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-green-800">
                      Component Generated: {result.component.name}
                    </span>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleComponentGenerated(result.component)}
                      className="border-green-300 text-green-700 hover:bg-green-100"
                    >
                      <Play className="w-3 h-3 mr-1" />
                      Add to Editor
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => {
                        navigator.clipboard.writeText(result.component.jsx)
                        toast.success('Component code copied to clipboard!')
                      }}
                      className="border-green-300 text-green-700 hover:bg-green-100"
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                <div className="text-xs text-green-700 mb-2">
                  {result.component.description}
                </div>
                <Badge variant="secondary" className="text-xs">
                  {result.component.category}
                </Badge>
              </Card>
            )
          } else {
            return (
              <Card className="p-4 bg-red-50 border-red-200">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-sm font-medium text-red-800">
                    Generation Failed
                  </span>
                </div>
                <div className="text-xs text-red-700">
                  {result.error || result.message || 'Unknown error occurred'}
                </div>
              </Card>
            )
          }
        }
        return (
          <div className="flex items-center space-x-2 text-blue-600">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm">Generating component...</span>
          </div>
        )
        
      case 'analyzeComponent':
        if (toolInvocation.state === 'result') {
          const result = toolInvocation.result
          return (
            <Card className="p-4 bg-blue-50 border-blue-200">
              <div className="text-sm font-medium text-blue-800 mb-2">
                Component Analysis: {result.componentName}
              </div>
              <div className="text-xs text-blue-700">
                {result.analysis}
              </div>
            </Card>
          )
        }
        return (
          <div className="flex items-center space-x-2 text-blue-600">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm">Analyzing component...</span>
          </div>
        )
        
      case 'optimizeComponent':
        if (toolInvocation.state === 'result') {
          const result = toolInvocation.result
          if (result.success) {
            return (
              <Card className="p-4 bg-purple-50 border-purple-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-purple-800">
                    Component Optimized
                  </span>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(result.optimizedCode)
                      toast.success('Optimized code copied to clipboard!')
                    }}
                    className="border-purple-300 text-purple-700 hover:bg-purple-100"
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                </div>
                <div className="text-xs text-purple-700 mb-2">
                  {result.improvements}
                </div>
                <div className="text-xs text-purple-600">
                  Changes: {result.changes?.join(', ')}
                </div>
              </Card>
            )
          }
        }
        return (
          <div className="flex items-center space-x-2 text-purple-600">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm">Optimizing component...</span>
          </div>
        )
        
      default:
        return (
          <div className="text-xs text-gray-500">
            Tool: {toolInvocation.toolName} - {toolInvocation.state}
          </div>
        )
    }
  }

  return (
    <div className="h-full flex flex-col bg-white border-r border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">AI Assistant</h2>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          Describe what you want to build and I'll generate it for you
        </p>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <Sparkles className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <div className="text-lg font-medium mb-2">Start Creating</div>
            <div className="text-sm">
              Ask me to create components like:
              <ul className="mt-2 space-y-1 text-left">
                <li>• "Create a hero section with a title and CTA button"</li>
                <li>• "Build a pricing card component"</li>
                <li>• "Generate a contact form with validation"</li>
              </ul>
            </div>
          </div>
        ) : (
          messages.map(renderMessage)
        )}
        
        {isLoading && (
          <div className="flex items-center space-x-2 text-blue-600 mb-4">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm">AI is thinking...</span>
          </div>
        )}
      </ScrollArea>

      {/* Input */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <form onSubmit={handleFormSubmit} className="flex space-x-2">
          <Input
            value={input}
            onChange={handleInputChange}
            placeholder="Create a hero section with..."
            className="flex-1"
            disabled={isLoading}
          />
          <Button type="submit" disabled={isLoading || !input.trim()}>
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              'Send'
            )}
          </Button>
        </form>
      </div>
    </div>
  )
}
