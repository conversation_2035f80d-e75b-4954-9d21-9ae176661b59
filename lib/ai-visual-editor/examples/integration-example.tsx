'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  AIVisualEditorLayout,
  useEditorStore,
  GeneratedComponent
} from '@/lib/ai-visual-editor'
import { toast } from 'sonner'

/**
 * Example: Integrating AI Visual Editor with existing application
 * 
 * This example shows how to:
 * 1. Use the AI Visual Editor as a standalone component
 * 2. Export generated components for use in other parts of the app
 * 3. Import/save components to a database or file system
 * 4. Customize the editor behavior
 */

interface IntegrationExampleProps {
  onComponentSaved?: (component: GeneratedComponent) => void
  onComponentsExported?: (components: GeneratedComponent[]) => void
  initialComponents?: GeneratedComponent[]
}

export function IntegrationExample({ 
  onComponentSaved,
  onComponentsExported,
  initialComponents = []
}: IntegrationExampleProps) {
  const [showEditor, setShowEditor] = useState(false)
  const [savedComponents, setSavedComponents] = useState<GeneratedComponent[]>(initialComponents)
  
  const { components, selectedComponentId, addComponent } = useEditorStore()

  // Load initial components into the editor
  const handleLoadComponents = () => {
    savedComponents.forEach(component => {
      addComponent(component)
    })
    toast.success(`Loaded ${savedComponents.length} components`)
  }

  // Save a single component
  const handleSaveComponent = (component: GeneratedComponent) => {
    setSavedComponents(prev => [...prev, component])
    onComponentSaved?.(component)
    toast.success(`Saved ${component.name}`)
  }

  // Export all components
  const handleExportComponents = () => {
    if (components.length === 0) {
      toast.error('No components to export')
      return
    }

    // Create export data
    const exportData = {
      components,
      metadata: {
        exportedAt: new Date().toISOString(),
        totalComponents: components.length,
        categories: [...new Set(components.map(c => c.category))]
      }
    }

    // Call parent handler
    onComponentsExported?.(components)

    // Download as JSON file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `components-export-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success('Components exported successfully!')
  }

  // Import components from file
  const handleImportComponents = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        const importedComponents = data.components || []
        
        importedComponents.forEach((component: GeneratedComponent) => {
          addComponent({
            ...component,
            id: `imported_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            createdAt: new Date(),
            updatedAt: new Date()
          })
        })

        toast.success(`Imported ${importedComponents.length} components`)
      } catch (error) {
        console.error('Import error:', error)
        toast.error('Failed to import components')
      }
    }
    reader.readAsText(file)
  }

  if (showEditor) {
    return (
      <div className="h-screen flex flex-col">
        {/* Editor Header */}
        <div className="h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-semibold">Component Editor</h1>
            <Badge variant="secondary">
              {components.length} component{components.length !== 1 ? 's' : ''}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="file"
              accept=".json"
              onChange={handleImportComponents}
              className="hidden"
              id="import-components"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => document.getElementById('import-components')?.click()}
            >
              Import
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportComponents}
              disabled={components.length === 0}
            >
              Export All
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowEditor(false)}
            >
              Close Editor
            </Button>
          </div>
        </div>

        {/* AI Visual Editor */}
        <div className="flex-1">
          <AIVisualEditorLayout />
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">AI Visual Editor Integration</h1>
        <p className="text-gray-600 mb-6">
          This example demonstrates how to integrate the AI Visual Editor into your application.
          You can generate components, save them, and use them throughout your app.
        </p>
        
        <div className="flex items-center space-x-4">
          <Button onClick={() => setShowEditor(true)} size="lg">
            Open AI Editor
          </Button>
          
          {savedComponents.length > 0 && (
            <Button variant="outline" onClick={handleLoadComponents}>
              Load Saved Components ({savedComponents.length})
            </Button>
          )}
        </div>
      </div>

      {/* Saved Components Display */}
      {savedComponents.length > 0 && (
        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Saved Components</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {savedComponents.map((component) => (
              <Card key={component.id} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">{component.name}</h3>
                  <Badge variant="outline">{component.category}</Badge>
                </div>
                <p className="text-sm text-gray-600 mb-3">{component.description}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{Object.keys(component.propertiesConfig).length} property sections</span>
                  <span>{new Date(component.createdAt).toLocaleDateString()}</span>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Usage Examples */}
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-semibold mb-4">Usage Examples</h2>
          
          <div className="space-y-4">
            <Card className="p-4">
              <h3 className="font-medium mb-2">1. Basic Integration</h3>
              <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`import { AIVisualEditorLayout } from '@/lib/ai-visual-editor'

export default function EditorPage() {
  return <AIVisualEditorLayout />
}`}
              </pre>
            </Card>

            <Card className="p-4">
              <h3 className="font-medium mb-2">2. Custom Integration with Store</h3>
              <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`import { useEditorStore } from '@/lib/ai-visual-editor'

function MyComponent() {
  const { components, addComponent } = useEditorStore()
  
  const handleAddComponent = (component) => {
    addComponent(component)
    // Save to database or perform other actions
  }
  
  return (
    <div>
      {components.map(comp => (
        <div key={comp.id}>{comp.name}</div>
      ))}
    </div>
  )
}`}
              </pre>
            </Card>

            <Card className="p-4">
              <h3 className="font-medium mb-2">3. Component Export/Import</h3>
              <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`// Export components
const exportData = {
  components: components.map(comp => ({
    name: comp.name,
    jsx: comp.jsx,
    defaultValues: comp.defaultValues
  }))
}

// Import and use components
importedComponents.forEach(comp => {
  addComponent(comp)
})`}
              </pre>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

// Example of using the integration in a Next.js page
export default function ExamplePage() {
  const handleComponentSaved = (component: GeneratedComponent) => {
    console.log('Component saved:', component)
    // Save to database, send to API, etc.
  }

  const handleComponentsExported = (components: GeneratedComponent[]) => {
    console.log('Components exported:', components)
    // Process exported components
  }

  return (
    <IntegrationExample
      onComponentSaved={handleComponentSaved}
      onComponentsExported={handleComponentsExported}
    />
  )
}
