# Next.js Layout & Page Generation Extension

## 🚀 **Overview**

The AI Visual Editor has been extended to generate complete Next.js layouts and pages, making it a comprehensive solution for building entire Next.js applications with AI assistance.

## ✨ **Key Features**

### **🏗️ Layout Generation**
- **Root Layouts**: Main app layouts with metadata and global components
- **Nested Layouts**: Route-specific layouts for sections like dashboard, blog, etc.
- **Templates**: Re-rendering layouts for dynamic content
- **Route Groups**: Organizational layouts for better structure

### **📄 Page Generation**
- **Static Pages**: Traditional static pages with SEO optimization
- **Dynamic Pages**: `[param]` dynamic routes with generateStaticParams
- **Catch-All Routes**: `[...param]` for flexible routing
- **Optional Catch-All**: `[[...param]]` for optional segments

### **🎨 Page Types**
- **Landing Pages**: Hero sections, features, testimonials, CTAs
- **Product Pages**: Galleries, details, reviews, related products
- **Blog Pages**: Content, sidebar, related posts, comments
- **Dashboard Pages**: Metrics, charts, tables, navigation
- **Auth Pages**: Login/register forms, social auth, validation
- **Error Pages**: Custom error handling with navigation

## 🛠️ **Technical Implementation**

### **Core Components**

#### **NextJSGenerator Service**
```typescript
import { NextJSGenerator } from '@/lib/ai-visual-editor'

// Generate a layout
const layout = await NextJSGenerator.generateLayout({
  name: 'Main Layout',
  description: 'Root application layout',
  type: 'root',
  features: ['header', 'footer'],
  styling: 'modern',
  includeHeader: true,
  includeFooter: true,
  responsive: true,
  accessibility: true,
  seo: true
})

// Generate a page
const page = await NextJSGenerator.generatePage({
  name: 'Home Page',
  description: 'Landing page with hero and features',
  route: '/',
  type: 'static',
  pageType: 'landing',
  features: ['hero', 'features', 'cta'],
  styling: 'modern',
  seo: true
})
```

#### **NextJSLayoutGenerator Component**
```typescript
import { NextJSLayoutGenerator } from '@/lib/ai-visual-editor'

export function MyApp() {
  return <NextJSLayoutGenerator />
}
```

### **AI Integration**

The system includes enhanced AI tools for Next.js generation:

#### **generateNextJSLayout Tool**
- Generates complete layouts with components
- Includes metadata configuration
- Handles responsive design and accessibility
- Creates proper file structure

#### **generateNextJSPage Tool**
- Creates pages with appropriate components
- Handles dynamic routing
- Includes SEO metadata
- Generates static params when needed

#### **generateFullNextJSProject Tool**
- Creates complete project structures
- Multiple layouts and pages
- Conditional features (auth, blog, ecommerce)
- Proper file organization

## 📁 **Generated File Structure**

```
app/
├── layout.tsx                 # Root layout
├── page.tsx                   # Home page
├── globals.css               # Global styles
├── (dashboard)/              # Route group
│   ├── layout.tsx           # Dashboard layout
│   └── page.tsx             # Dashboard page
├── blog/
│   ├── page.tsx             # Blog listing
│   └── [slug]/
│       └── page.tsx         # Blog post
├── products/
│   ├── page.tsx             # Product listing
│   └── [id]/
│       └── page.tsx         # Product details
└── auth/
    ├── login/
    │   └── page.tsx         # Login page
    └── register/
        └── page.tsx         # Register page
```

## 🎯 **Usage Examples**

### **1. Generate a Root Layout**

```typescript
const rootLayout = await NextJSGenerator.generateLayout({
  name: 'App Layout',
  description: 'Main application layout with header and footer',
  type: 'root',
  features: ['navigation', 'footer', 'theme-provider'],
  styling: 'modern',
  includeHeader: true,
  includeFooter: true,
  includeSidebar: false,
  responsive: true,
  accessibility: true,
  seo: true
})
```

**Generated Output:**
```tsx
import type { Metadata } from 'next'
import { Header } from '@/components/header'
import { Footer } from '@/components/footer'

export const metadata: Metadata = {
  title: {
    default: 'App Layout',
    template: '%s | App Layout'
  },
  description: 'Main application layout with header and footer',
}

export default function AppLayoutLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="modern">
      <body className="min-h-screen bg-background font-sans antialiased">
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}
```

### **2. Generate a Landing Page**

```typescript
const landingPage = await NextJSGenerator.generatePage({
  name: 'Home Page',
  description: 'Modern landing page with hero section',
  route: '/',
  type: 'static',
  pageType: 'landing',
  features: ['hero', 'features', 'testimonials', 'cta'],
  styling: 'modern',
  seo: true,
  generateStaticParams: false
})
```

**Generated Output:**
```tsx
import type { Metadata } from 'next'
import { HeroSection } from '@/components/herosection'
import { FeaturesSection } from '@/components/featuressection'
import { TestimonialsSection } from '@/components/testimonialssection'
import { CTASection } from '@/components/ctasection'

export const metadata: Metadata = {
  title: 'Home Page',
  description: 'Modern landing page with hero section',
}

export default function HomePage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <HeroSection />
      <FeaturesSection />
      <TestimonialsSection />
      <CTASection />
    </div>
  )
}
```

### **3. Generate a Dynamic Product Page**

```typescript
const productPage = await NextJSGenerator.generatePage({
  name: 'Product Page',
  description: 'Dynamic product page with gallery and details',
  route: '/products/[id]',
  type: 'dynamic',
  pageType: 'product',
  features: ['gallery', 'details', 'reviews', 'related'],
  styling: 'modern',
  seo: true,
  generateStaticParams: true
})
```

### **4. Generate Complete Project**

```typescript
const project = await NextJSGenerator.generateProject(
  [
    // Layouts
    {
      name: 'Root Layout',
      type: 'root',
      features: ['header', 'footer'],
      // ... other params
    },
    {
      name: 'Dashboard Layout',
      type: 'nested',
      features: ['sidebar', 'header'],
      // ... other params
    }
  ],
  [
    // Pages
    {
      name: 'Home Page',
      route: '/',
      pageType: 'landing',
      // ... other params
    },
    {
      name: 'Dashboard',
      route: '/dashboard',
      pageType: 'dashboard',
      // ... other params
    }
  ]
)
```

## 🎨 **Styling Options**

The system supports multiple styling approaches:

- **Modern**: Clean, contemporary design with gradients and shadows
- **Minimal**: Simple, clean design with lots of whitespace
- **Bold**: High contrast, vibrant colors, strong typography
- **Elegant**: Sophisticated, refined design with subtle details
- **Playful**: Fun, colorful design with rounded corners and animations

## 🔧 **Advanced Features**

### **Metadata Generation**
Automatic SEO-optimized metadata including:
- Title templates
- Descriptions and keywords
- Open Graph tags
- Twitter cards
- Robots configuration
- Canonical URLs

### **Route Handling**
Proper handling of all Next.js route types:
- Static routes: `/about`
- Dynamic routes: `/products/[id]`
- Catch-all routes: `/blog/[...slug]`
- Optional catch-all: `/docs/[[...slug]]`

### **Component Integration**
Generated layouts and pages include:
- Proper TypeScript interfaces
- Responsive design patterns
- Accessibility features
- Error boundaries
- Loading states

### **Performance Optimization**
- Automatic code splitting
- Static generation when appropriate
- Optimized imports
- Memoization suggestions

## 🚀 **Integration with AI Visual Editor**

The Next.js extension seamlessly integrates with the existing AI Visual Editor:

1. **Component Reuse**: Generated components can be used in layouts and pages
2. **Property System**: Full integration with the dynamic properties panel
3. **Live Preview**: Real-time preview of generated layouts and pages
4. **Export Options**: Download complete Next.js projects

## 📊 **Benefits**

### **For Developers**
- **Rapid Prototyping**: Generate complete applications in minutes
- **Best Practices**: Follows Next.js 14+ conventions and patterns
- **Type Safety**: Full TypeScript support with proper interfaces
- **SEO Ready**: Built-in metadata and optimization

### **For Designers**
- **Visual Interface**: No-code layout and page creation
- **Responsive Design**: Automatic mobile-first responsive layouts
- **Styling Control**: Multiple design approaches and customization
- **Component Library**: Reusable components across projects

### **For Teams**
- **Consistency**: Standardized layouts and page structures
- **Collaboration**: Shareable templates and components
- **Scalability**: Proper file organization and architecture
- **Maintenance**: Clean, documented code generation

## 🔮 **Future Enhancements**

- **Database Integration**: Automatic API route generation
- **Authentication**: Built-in auth system integration
- **Deployment**: One-click deployment to Vercel/Netlify
- **CMS Integration**: Headless CMS connections
- **E-commerce**: Complete e-commerce functionality
- **Analytics**: Built-in analytics and tracking

## 🎯 **Getting Started**

1. **Access the Generator**: Navigate to `/admin/nextjs-generator`
2. **Choose Layout or Page**: Select what you want to generate
3. **Configure Options**: Set styling, features, and requirements
4. **Generate**: Let AI create your Next.js code
5. **Download**: Get complete, ready-to-use files
6. **Integrate**: Add to your Next.js project

The Next.js extension transforms the AI Visual Editor from a component generator into a complete application builder, enabling rapid development of professional Next.js applications with AI assistance.
