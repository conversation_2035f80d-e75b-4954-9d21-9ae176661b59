import { 
  NextJSLayout, 
  NextJSPage, 
  LayoutGenerationParams, 
  PageGenerationParams,
  NextJSMetadata,
  GeneratedNextJSStructure,
  RouteSegment
} from '../types/nextjs-types'
import { GeneratedComponent } from '../types'
import { generateComponentId } from '../utils/component-analyzer'

export class NextJSGenerator {
  
  /**
   * Generate a complete Next.js layout
   */
  static async generateLayout(params: LayoutGenerationParams): Promise<NextJSLayout> {
    const layoutId = generateComponentId()
    const components: GeneratedComponent[] = []
    
    // Generate layout components based on features
    if (params.includeHeader) {
      components.push(await this.generateHeaderComponent(params))
    }
    
    if (params.includeFooter) {
      components.push(await this.generateFooterComponent(params))
    }
    
    if (params.includeSidebar) {
      components.push(await this.generateSidebarComponent(params))
    }
    
    // Generate the layout code
    const layoutCode = this.generateLayoutCode(params, components)
    
    return {
      id: layoutId,
      name: params.name,
      description: params.description,
      type: params.type,
      path: this.generateLayoutPath(params),
      code: layoutCode,
      metadata: params.seo ? this.generateDefaultMetadata(params) : undefined,
      components,
      slots: this.generateLayoutSlots(params),
      dependencies: this.extractLayoutDependencies(params, components),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  
  /**
   * Generate a complete Next.js page
   */
  static async generatePage(params: PageGenerationParams): Promise<NextJSPage> {
    const pageId = generateComponentId()
    const components: GeneratedComponent[] = []
    
    // Generate page components based on page type
    switch (params.pageType) {
      case 'landing':
        components.push(
          await this.generateHeroComponent(params),
          await this.generateFeaturesComponent(params),
          await this.generateCTAComponent(params)
        )
        break
        
      case 'product':
        components.push(
          await this.generateProductGalleryComponent(params),
          await this.generateProductDetailsComponent(params),
          await this.generateRelatedProductsComponent(params)
        )
        break
        
      case 'blog':
        components.push(
          await this.generateBlogHeaderComponent(params),
          await this.generateBlogContentComponent(params),
          await this.generateBlogSidebarComponent(params)
        )
        break
        
      case 'dashboard':
        components.push(
          await this.generateDashboardHeaderComponent(params),
          await this.generateMetricsComponent(params),
          await this.generateChartsComponent(params),
          await this.generateDataTableComponent(params)
        )
        break
        
      default:
        components.push(await this.generateGenericPageComponent(params))
    }
    
    // Generate the page code
    const pageCode = this.generatePageCode(params, components)
    
    return {
      id: pageId,
      name: params.name,
      description: params.description,
      route: params.route,
      type: params.type,
      code: pageCode,
      metadata: params.seo ? this.generatePageMetadata(params) : undefined,
      components,
      layout: params.layout,
      generateStaticParams: params.generateStaticParams,
      revalidate: params.revalidate,
      dependencies: this.extractPageDependencies(params, components),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  
  /**
   * Generate complete Next.js project structure
   */
  static async generateProject(
    layouts: LayoutGenerationParams[],
    pages: PageGenerationParams[]
  ): Promise<GeneratedNextJSStructure> {
    const generatedLayouts = await Promise.all(
      layouts.map(params => this.generateLayout(params))
    )
    
    const generatedPages = await Promise.all(
      pages.map(params => this.generatePage(params))
    )
    
    const allComponents = [
      ...generatedLayouts.flatMap(l => l.components),
      ...generatedPages.flatMap(p => p.components)
    ]
    
    const routes = generatedPages.map(p => p.route)
    const fileStructure = this.generateFileStructure(generatedLayouts, generatedPages)
    
    return {
      layouts: generatedLayouts,
      pages: generatedPages,
      components: allComponents,
      routes,
      fileStructure
    }
  }
  
  /**
   * Generate layout code
   */
  private static generateLayoutCode(
    params: LayoutGenerationParams, 
    components: GeneratedComponent[]
  ): string {
    const imports = this.generateImports(components)
    const metadata = params.seo ? this.generateMetadataExport(params) : ''
    
    return `${imports}

${metadata}

export default function ${this.toPascalCase(params.name)}Layout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="${params.styling}">
      <body className="min-h-screen bg-background font-sans antialiased">
        ${params.includeHeader ? '<Header />' : ''}
        <main className="flex-1">
          {children}
        </main>
        ${params.includeFooter ? '<Footer />' : ''}
        ${params.includeSidebar ? '<Sidebar />' : ''}
      </body>
    </html>
  )
}`
  }
  
  /**
   * Generate page code
   */
  private static generatePageCode(
    params: PageGenerationParams,
    components: GeneratedComponent[]
  ): string {
    const imports = this.generateImports(components)
    const metadata = params.seo ? this.generateMetadataExport(params) : ''
    const staticParams = params.generateStaticParams ? this.generateStaticParamsFunction(params) : ''
    
    const componentJSX = components.map(comp => 
      `<${comp.name} />`
    ).join('\n        ')
    
    return `${imports}

${metadata}

${staticParams}

export default function ${this.toPascalCase(params.name)}Page() {
  return (
    <div className="container mx-auto px-4 py-8">
      ${componentJSX}
    </div>
  )
}`
  }
  
  /**
   * Generate component based on type
   */
  private static async generateHeaderComponent(params: LayoutGenerationParams): Promise<GeneratedComponent> {
    return {
      id: generateComponentId(),
      name: 'Header',
      description: 'Site header with navigation',
      category: 'navigation',
      jsx: `
export function Header() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 hidden md:flex">
          <a className="mr-6 flex items-center space-x-2" href="/">
            <span className="hidden font-bold sm:inline-block">
              ${params.name}
            </span>
          </a>
        </div>
        <nav className="flex items-center space-x-6 text-sm font-medium">
          <a href="/products">Products</a>
          <a href="/about">About</a>
          <a href="/contact">Contact</a>
        </nav>
      </div>
    </header>
  )
}`,
      props: {},
      propertiesConfig: {
        appearance: [],
        content: [],
        behavior: [],
        data: [],
        layout: []
      },
      defaultValues: {},
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  
  private static async generateFooterComponent(params: LayoutGenerationParams): Promise<GeneratedComponent> {
    return {
      id: generateComponentId(),
      name: 'Footer',
      description: 'Site footer with links and information',
      category: 'navigation',
      jsx: `
export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container py-8 md:py-12">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">${params.name}</h3>
            <p className="text-sm text-muted-foreground">
              Building amazing experiences with AI-powered components.
            </p>
          </div>
          <div className="space-y-3">
            <h4 className="text-sm font-semibold">Products</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="/products">All Products</a></li>
              <li><a href="/new">New Arrivals</a></li>
              <li><a href="/sale">Sale</a></li>
            </ul>
          </div>
          <div className="space-y-3">
            <h4 className="text-sm font-semibold">Company</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="/about">About</a></li>
              <li><a href="/contact">Contact</a></li>
              <li><a href="/careers">Careers</a></li>
            </ul>
          </div>
          <div className="space-y-3">
            <h4 className="text-sm font-semibold">Legal</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="/privacy">Privacy</a></li>
              <li><a href="/terms">Terms</a></li>
              <li><a href="/cookies">Cookies</a></li>
            </ul>
          </div>
        </div>
        <div className="mt-8 border-t pt-8 text-center text-sm text-muted-foreground">
          © 2024 ${params.name}. All rights reserved.
        </div>
      </div>
    </footer>
  )
}`,
      props: {},
      propertiesConfig: {
        appearance: [],
        content: [],
        behavior: [],
        data: [],
        layout: []
      },
      defaultValues: {},
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  
  // Additional component generators would go here...
  private static async generateHeroComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    return {
      id: generateComponentId(),
      name: 'HeroSection',
      description: 'Hero section for landing page',
      category: 'layout',
      jsx: `
export function HeroSection() {
  return (
    <section className="relative py-20 lg:py-32">
      <div className="container">
        <div className="mx-auto max-w-3xl text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
            Welcome to ${params.name}
          </h1>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Discover amazing products and services that will transform your experience.
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <button className="rounded-md bg-primary px-3.5 py-2.5 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90">
              Get Started
            </button>
            <a href="#" className="text-sm font-semibold leading-6">
              Learn more <span aria-hidden="true">→</span>
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}`,
      props: {},
      propertiesConfig: {
        appearance: [],
        content: [],
        behavior: [],
        data: [],
        layout: []
      },
      defaultValues: {},
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
  
  // Utility methods
  private static generateLayoutPath(params: LayoutGenerationParams): string {
    switch (params.type) {
      case 'root':
        return 'app/layout.tsx'
      case 'nested':
        return `app/${params.name.toLowerCase()}/layout.tsx`
      case 'template':
        return `app/${params.name.toLowerCase()}/template.tsx`
      case 'group':
        return `app/(${params.name.toLowerCase()})/layout.tsx`
      default:
        return 'app/layout.tsx'
    }
  }
  
  private static generateDefaultMetadata(params: LayoutGenerationParams): NextJSMetadata {
    return {
      title: {
        default: params.name,
        template: `%s | ${params.name}`
      },
      description: params.description,
      keywords: ['nextjs', 'react', 'typescript', 'tailwindcss'],
      authors: [{ name: params.name }],
      creator: params.name,
      openGraph: {
        type: 'website',
        locale: 'en_US',
        siteName: params.name,
        title: params.name,
        description: params.description
      },
      twitter: {
        card: 'summary_large_image',
        site: `@${params.name.toLowerCase()}`,
        creator: `@${params.name.toLowerCase()}`
      },
      robots: {
        index: true,
        follow: true
      }
    }
  }
  
  private static toPascalCase(str: string): string {
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase()
    }).replace(/\s+/g, '')
  }
  
  private static generateImports(components: GeneratedComponent[]): string {
    const imports = components.map(comp => 
      `import { ${comp.name} } from '@/components/${comp.name.toLowerCase()}'`
    )
    
    return [
      "import type { Metadata } from 'next'",
      ...imports
    ].join('\n')
  }
  
  private static generateMetadataExport(params: any): string {
    return `
export const metadata: Metadata = {
  title: '${params.name}',
  description: '${params.description}',
}`
  }
  
  private static generateStaticParamsFunction(params: PageGenerationParams): string {
    if (!params.generateStaticParams) return ''
    
    return `
export async function generateStaticParams() {
  // Generate static params for ${params.route}
  return []
}`
  }
  
  private static generateLayoutSlots(params: LayoutGenerationParams) {
    return [
      {
        id: 'children',
        name: 'Children',
        description: 'Main content area',
        type: 'children' as const,
        required: true
      }
    ]
  }
  
  private static extractLayoutDependencies(params: LayoutGenerationParams, components: GeneratedComponent[]): string[] {
    const deps = ['react', 'next']
    if (params.styling === 'modern') deps.push('tailwindcss')
    return deps
  }
  
  private static extractPageDependencies(params: PageGenerationParams, components: GeneratedComponent[]): string[] {
    const deps = ['react', 'next']
    if (params.generateStaticParams) deps.push('next/static')
    return deps
  }
  
  private static generateFileStructure(layouts: NextJSLayout[], pages: NextJSPage[]) {
    // Generate file structure representation
    return []
  }
  
  // Placeholder methods for other component types
  private static async generateSidebarComponent(params: LayoutGenerationParams): Promise<GeneratedComponent> {
    // Implementation for sidebar component
    return {} as GeneratedComponent
  }
  
  private static async generateFeaturesComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for features component
    return {} as GeneratedComponent
  }
  
  private static async generateCTAComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for CTA component
    return {} as GeneratedComponent
  }
  
  private static async generateProductGalleryComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for product gallery component
    return {} as GeneratedComponent
  }
  
  private static async generateProductDetailsComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for product details component
    return {} as GeneratedComponent
  }
  
  private static async generateRelatedProductsComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for related products component
    return {} as GeneratedComponent
  }
  
  private static async generateBlogHeaderComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for blog header component
    return {} as GeneratedComponent
  }
  
  private static async generateBlogContentComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for blog content component
    return {} as GeneratedComponent
  }
  
  private static async generateBlogSidebarComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for blog sidebar component
    return {} as GeneratedComponent
  }
  
  private static async generateDashboardHeaderComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for dashboard header component
    return {} as GeneratedComponent
  }
  
  private static async generateMetricsComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for metrics component
    return {} as GeneratedComponent
  }
  
  private static async generateChartsComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for charts component
    return {} as GeneratedComponent
  }
  
  private static async generateDataTableComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for data table component
    return {} as GeneratedComponent
  }
  
  private static async generateGenericPageComponent(params: PageGenerationParams): Promise<GeneratedComponent> {
    // Implementation for generic page component
    return {} as GeneratedComponent
  }
  
  private static generatePageMetadata(params: PageGenerationParams): NextJSMetadata {
    return {
      title: params.name,
      description: params.description
    }
  }
}
