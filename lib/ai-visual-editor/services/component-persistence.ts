import { GeneratedComponent } from '../types'

export interface ComponentTemplate {
  id: string
  name: string
  description: string
  category: string
  jsx: string
  propertiesConfig: any
  defaultValues: Record<string, any>
  tags: string[]
  isPublic: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
  usageCount: number
  rating: number
  version: string
}

export interface ComponentVersion {
  id: string
  componentId: string
  version: string
  jsx: string
  propertiesConfig: any
  defaultValues: Record<string, any>
  changelog: string
  createdAt: Date
  createdBy: string
}

export interface ComponentUsage {
  id: string
  componentId: string
  pageId?: string
  blockId?: string
  projectId?: string
  usedAt: Date
  context: 'page' | 'block' | 'template' | 'export'
}

export class ComponentPersistenceService {
  private baseUrl = '/api/ai-visual-editor'

  // Save component as template
  async saveAsTemplate(component: GeneratedComponent, metadata: {
    tags?: string[]
    isPublic?: boolean
    description?: string
  }): Promise<ComponentTemplate> {
    const response = await fetch(`${this.baseUrl}/templates`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: component.name,
        description: metadata.description || component.description,
        category: component.category,
        jsx: component.jsx,
        propertiesConfig: component.propertiesConfig,
        defaultValues: component.defaultValues,
        tags: metadata.tags || [],
        isPublic: metadata.isPublic || false
      })
    })

    if (!response.ok) {
      throw new Error('Failed to save component template')
    }

    return response.json()
  }

  // Load component templates
  async getTemplates(filters?: {
    category?: string
    tags?: string[]
    search?: string
    isPublic?: boolean
  }): Promise<ComponentTemplate[]> {
    const params = new URLSearchParams()
    if (filters?.category) params.append('category', filters.category)
    if (filters?.tags) params.append('tags', filters.tags.join(','))
    if (filters?.search) params.append('search', filters.search)
    if (filters?.isPublic !== undefined) params.append('isPublic', String(filters.isPublic))

    const response = await fetch(`${this.baseUrl}/templates?${params}`)
    if (!response.ok) {
      throw new Error('Failed to load component templates')
    }

    return response.json()
  }

  // Save component to page/block system
  async saveToPage(component: GeneratedComponent, pageId: string, position?: number): Promise<void> {
    const blockData = this.convertToPageBlock(component)
    
    const response = await fetch(`/api/pages/${pageId}/blocks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...blockData,
        position: position || 0
      })
    })

    if (!response.ok) {
      throw new Error('Failed to save component to page')
    }

    // Track usage
    await this.trackUsage(component.id, { pageId, context: 'page' })
  }

  // Convert AI component to page block format
  private convertToPageBlock(component: GeneratedComponent) {
    return {
      blockType: 'ai-generated',
      isVisible: true,
      configuration: {
        componentId: component.id,
        componentName: component.name,
        jsx: component.jsx,
        propertiesConfig: component.propertiesConfig
      },
      content: component.defaultValues,
      styling: this.extractStylingFromProperties(component.defaultValues),
      responsive: this.generateResponsiveConfig(component.defaultValues),
      animation: this.extractAnimationConfig(component.defaultValues)
    }
  }

  // Extract styling configuration
  private extractStylingFromProperties(properties: Record<string, any>) {
    return {
      backgroundColor: properties.backgroundColor,
      textColor: properties.textColor,
      padding: properties.padding,
      margin: properties.margin,
      borderRadius: properties.borderRadius,
      shadow: properties.shadow,
      border: properties.border
    }
  }

  // Generate responsive configuration
  private generateResponsiveConfig(properties: Record<string, any>) {
    return {
      desktop: {
        width: properties.width || 'w-full',
        height: properties.height || 'h-auto',
        display: properties.display || 'block'
      },
      tablet: {
        width: 'w-full',
        height: 'h-auto',
        display: 'block'
      },
      mobile: {
        width: 'w-full',
        height: 'h-auto',
        display: 'block'
      }
    }
  }

  // Extract animation configuration
  private extractAnimationConfig(properties: Record<string, any>) {
    if (!properties.animation || properties.animation === 'none') {
      return null
    }

    return {
      type: properties.animation,
      duration: properties.animationDuration || 500,
      delay: 0,
      easing: 'ease-in-out'
    }
  }

  // Version management
  async createVersion(componentId: string, updates: Partial<GeneratedComponent>, changelog: string): Promise<ComponentVersion> {
    const response = await fetch(`${this.baseUrl}/components/${componentId}/versions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        jsx: updates.jsx,
        propertiesConfig: updates.propertiesConfig,
        defaultValues: updates.defaultValues,
        changelog
      })
    })

    if (!response.ok) {
      throw new Error('Failed to create component version')
    }

    return response.json()
  }

  // Get component versions
  async getVersions(componentId: string): Promise<ComponentVersion[]> {
    const response = await fetch(`${this.baseUrl}/components/${componentId}/versions`)
    if (!response.ok) {
      throw new Error('Failed to load component versions')
    }

    return response.json()
  }

  // Track component usage
  async trackUsage(componentId: string, context: {
    pageId?: string
    blockId?: string
    projectId?: string
    context: 'page' | 'block' | 'template' | 'export'
  }): Promise<void> {
    await fetch(`${this.baseUrl}/components/${componentId}/usage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(context)
    })
  }

  // Export components
  async exportComponents(componentIds: string[], format: 'json' | 'zip' | 'npm'): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/export`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ componentIds, format })
    })

    if (!response.ok) {
      throw new Error('Failed to export components')
    }

    return response.blob()
  }

  // Import components
  async importComponents(file: File): Promise<GeneratedComponent[]> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch(`${this.baseUrl}/import`, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error('Failed to import components')
    }

    return response.json()
  }

  // Search components
  async searchComponents(query: string, filters?: {
    category?: string
    tags?: string[]
    complexity?: string
    hasData?: boolean
  }): Promise<ComponentTemplate[]> {
    const params = new URLSearchParams()
    params.append('q', query)
    if (filters?.category) params.append('category', filters.category)
    if (filters?.tags) params.append('tags', filters.tags.join(','))
    if (filters?.complexity) params.append('complexity', filters.complexity)
    if (filters?.hasData !== undefined) params.append('hasData', String(filters.hasData))

    const response = await fetch(`${this.baseUrl}/search?${params}`)
    if (!response.ok) {
      throw new Error('Failed to search components')
    }

    return response.json()
  }

  // Get component analytics
  async getAnalytics(componentId: string): Promise<{
    usageCount: number
    popularPages: string[]
    performanceMetrics: any
    userFeedback: any[]
  }> {
    const response = await fetch(`${this.baseUrl}/components/${componentId}/analytics`)
    if (!response.ok) {
      throw new Error('Failed to load component analytics')
    }

    return response.json()
  }

  // Duplicate component
  async duplicateComponent(componentId: string, newName: string): Promise<GeneratedComponent> {
    const response = await fetch(`${this.baseUrl}/components/${componentId}/duplicate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name: newName })
    })

    if (!response.ok) {
      throw new Error('Failed to duplicate component')
    }

    return response.json()
  }

  // Share component
  async shareComponent(componentId: string, shareSettings: {
    isPublic: boolean
    allowComments: boolean
    allowForks: boolean
    expiresAt?: Date
  }): Promise<{ shareUrl: string; shareId: string }> {
    const response = await fetch(`${this.baseUrl}/components/${componentId}/share`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(shareSettings)
    })

    if (!response.ok) {
      throw new Error('Failed to share component')
    }

    return response.json()
  }
}

// Singleton instance
export const componentPersistence = new ComponentPersistenceService()
