# AI Visual Editor

A complete AI-powered no-code visual editor system that generates React components with dynamic properties panels.

## 🚀 **Latest Enhancements**

### **Security & Performance**
- ✅ **Secure Component Rendering**: Replaced `dangerouslySetInnerHTML` with safe React component creation
- ✅ **Advanced Component Analysis**: Enhanced AST parsing with complexity, accessibility, and performance analysis
- ✅ **Performance Monitoring**: Real-time performance metrics and optimization suggestions
- ✅ **Rate Limiting**: API protection with intelligent rate limiting and error handling

### **Enhanced User Experience**
- ✅ **Smart Property Suggestions**: AI-powered suggestions for better design and accessibility
- ✅ **Component Library**: Pre-built templates with search, filtering, and categorization
- ✅ **Advanced Properties Panel**: Search, history, presets, and smart recommendations
- ✅ **Database Integration**: Full persistence with versioning and analytics

### **Developer Experience**
- ✅ **Comprehensive Testing**: Integration tests, performance tests, and accessibility tests
- ✅ **TypeScript Excellence**: Full type safety with advanced interfaces and validation
- ✅ **Error Boundaries**: Graceful error handling with detailed feedback
- ✅ **Performance Hooks**: Built-in monitoring and optimization utilities

## Features

### 🤖 AI-Powered Component Generation
- **Advanced Natural Language Processing**: Convert complex descriptions to production-ready React components
- **Intelligent Component Analysis**: AST-based analysis for complexity, accessibility, and performance
- **Context-Aware Property Generation**: Automatically generate appropriate property fields
- **Multi-Complexity Support**: Simple, moderate, and complex component generation
- **Accessibility-First**: Built-in ARIA labels, semantic HTML, and keyboard navigation
- **Performance Optimization**: Automatic suggestions for memoization, virtualization, and code splitting

### 🎨 Enhanced Dynamic Properties Panel
- **Smart Suggestions**: AI-powered recommendations for color harmony, spacing, and accessibility
- **Advanced Search & Filtering**: Find properties quickly with intelligent search
- **Property History**: Undo/redo functionality with change tracking
- **Preset Management**: Save and load property configurations
- **Real-time Validation**: Instant feedback on property values
- **Organized Categories**: Appearance, Content, Behavior, Data, Layout with smart badges

### 📱 Advanced Live Preview System
- **Secure Rendering**: Safe component rendering with error boundaries
- **Performance Monitoring**: Real-time render time and memory usage tracking
- **Responsive Design**: Desktop, tablet, mobile with accurate viewport simulation
- **Interactive Selection**: Click-to-select with visual feedback
- **Error Recovery**: Graceful handling of rendering errors with suggestions

### 🗂️ Professional Component Management
- **Component Library**: Pre-built templates with ratings and usage statistics
- **Advanced Search**: Multi-criteria filtering with tag-based organization
- **Version Control**: Component versioning with changelog tracking
- **Analytics Dashboard**: Usage metrics, performance data, and user feedback
- **Export/Import**: Multiple formats (JSON, ZIP, NPM) with metadata preservation

## Architecture

### Core Components

#### `AIVisualEditorLayout`
Main layout component that integrates with the existing `EditorLayout` system.

#### `AiChatPanel`
AI chat interface for component generation using AI SDK tool calling.

#### `PropertiesPanel`
Dynamic properties editor using the existing custom fields system.

#### `LivePreview`
Real-time component preview with responsive design modes.

#### `ComponentTree`
Component management and organization interface.

#### `DynamicComponentRenderer`
Safe component rendering system with property application.

### State Management

Uses Zustand for centralized state management with persistence:

```typescript
interface EditorState {
  components: GeneratedComponent[]
  selectedComponentId: string | null
  propertyValues: Record<string, Record<string, any>>
  isGenerating: boolean
  previewMode: 'desktop' | 'tablet' | 'mobile'
  // ... more state
}
```

### AI Integration

Built with AI SDK for tool calling:

- `generateComponent`: Creates React components from descriptions
- `analyzeComponent`: Analyzes existing components for properties
- `optimizeComponent`: Improves component performance and accessibility

## Usage

### Basic Setup

```tsx
import { AIVisualEditorLayout } from '@/lib/ai-visual-editor'

export default function EditorPage() {
  return <AIVisualEditorLayout />
}
```

### Using Individual Components

```tsx
import { 
  AiChatPanel, 
  PropertiesPanel, 
  LivePreview,
  useEditorStore 
} from '@/lib/ai-visual-editor'

function CustomEditor() {
  const { components, selectedComponent } = useEditorStore()
  
  return (
    <div className="flex h-screen">
      <AiChatPanel />
      <PropertiesPanel />
      <LivePreview />
    </div>
  )
}
```

### Accessing the Store

```tsx
import { useEditorStore } from '@/lib/ai-visual-editor'

function MyComponent() {
  const { 
    components, 
    addComponent, 
    selectComponent,
    updatePropertyValue 
  } = useEditorStore()
  
  // Use store methods...
}
```

## API Routes

### `/api/ai-visual-editor/chat`

Main AI chat endpoint with tool calling support:

- **POST**: Send messages and receive AI responses with tool executions
- **Tools Available**:
  - `generateComponent`: Generate React components
  - `analyzeComponent`: Analyze component structure
  - `optimizeComponent`: Optimize existing components

## Component Generation

### Supported Component Types

- **Hero**: Landing page hero sections
- **Card**: Content cards and product displays
- **Button**: Interactive buttons with various styles
- **Form**: Contact forms and input components
- **Navigation**: Menu and navigation components
- **Footer**: Page footer sections
- **Custom**: Any custom component type

### Property Categories

#### Appearance
- Colors (background, text, borders)
- Spacing (padding, margin)
- Borders and shadows
- Typography and sizing

#### Content
- Text content (titles, descriptions)
- Media (images, icons)
- Links and navigation
- Rich text editing

#### Behavior
- Animations and transitions
- Hover effects
- Click actions
- Interactive states

#### Data
- Data sources (static, API, database)
- Dynamic content binding
- API endpoint configuration

#### Layout
- Positioning and sizing
- Responsive design
- Alignment and display

## Custom Fields Integration

The system uses the existing custom fields from:
`@lib/core/builders/components/properties-panel/custom-fields/`

Supported field types:
- `text`, `textarea`, `richtext`
- `color`, `gradient`
- `spacing`, `border`, `shadow`
- `select`, `boolean`, `range`
- `image`, `media`, `icon`
- `link`, `repeater`
- `animation`, `code`

## Error Handling

- Component validation and sanitization
- Safe rendering with error boundaries
- User-friendly error messages
- Automatic recovery mechanisms

## Performance

- Code splitting by module
- Lazy loading of AI features
- Optimistic updates
- Efficient state management
- Component memoization

## Security

- Component code sanitization
- XSS prevention
- Safe JSX rendering
- Input validation

## Development

### Adding New Component Types

1. Update the `componentType` enum in types
2. Add generation logic in AI tools
3. Update property field generation
4. Test with various scenarios

### Extending Property Fields

1. Add new field types to the custom fields system
2. Update property generators
3. Add field rendering logic
4. Update type definitions

### Customizing AI Behavior

1. Modify prompts in `/ai/prompts/`
2. Update tool definitions in API route
3. Adjust component analysis logic
4. Fine-tune property generation

## Troubleshooting

### Common Issues

1. **Component not rendering**: Check console for validation errors
2. **Properties not updating**: Verify field configuration and store updates
3. **AI not responding**: Check API key and network connectivity
4. **Export failing**: Ensure components have valid data

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG_AI_EDITOR=true
```

## Contributing

1. Follow existing code patterns
2. Add proper TypeScript types
3. Include error handling
4. Write tests for new features
5. Update documentation
