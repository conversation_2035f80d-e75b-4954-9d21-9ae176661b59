import { GeneratedComponent } from './index'

export interface NextJSLayout {
  id: string
  name: string
  description: string
  type: 'root' | 'nested' | 'template' | 'group'
  path: string // e.g., 'app/layout.tsx', 'app/(dashboard)/layout.tsx'
  code: string
  metadata?: NextJSMetadata
  components: GeneratedComponent[]
  slots: LayoutSlot[]
  dependencies: string[]
  createdAt: Date
  updatedAt: Date
}

export interface NextJSPage {
  id: string
  name: string
  description: string
  route: string // e.g., '/products/[id]', '/blog/[...slug]'
  type: 'static' | 'dynamic' | 'catch-all' | 'optional-catch-all'
  code: string
  metadata?: NextJSMetadata
  components: GeneratedComponent[]
  layout?: string // Layout ID to use
  generateStaticParams?: boolean
  revalidate?: number | false
  dependencies: string[]
  createdAt: Date
  updatedAt: Date
}

export interface NextJSMetadata {
  title?: string | MetadataTitle
  description?: string
  keywords?: string[]
  authors?: Array<{ name: string; url?: string }>
  creator?: string
  publisher?: string
  openGraph?: OpenGraphMetadata
  twitter?: TwitterMetadata
  robots?: RobotsMetadata
  alternates?: AlternatesMetadata
  icons?: IconsMetadata
  manifest?: string
  verification?: VerificationMetadata
}

export interface MetadataTitle {
  default: string
  template?: string
}

export interface OpenGraphMetadata {
  type?: string
  locale?: string
  url?: string
  siteName?: string
  title?: string
  description?: string
  images?: Array<{
    url: string
    width?: number
    height?: number
    alt?: string
  }>
}

export interface TwitterMetadata {
  card?: 'summary' | 'summary_large_image' | 'app' | 'player'
  site?: string
  creator?: string
  title?: string
  description?: string
  images?: string[]
}

export interface RobotsMetadata {
  index?: boolean
  follow?: boolean
  noarchive?: boolean
  nosnippet?: boolean
  noimageindex?: boolean
  nocache?: boolean
  googleBot?: {
    index?: boolean
    follow?: boolean
    'max-video-preview'?: number
    'max-image-preview'?: 'none' | 'standard' | 'large'
    'max-snippet'?: number
  }
}

export interface AlternatesMetadata {
  canonical?: string
  languages?: Record<string, string>
  media?: Record<string, string>
  types?: Record<string, string>
}

export interface IconsMetadata {
  icon?: string | Array<{ url: string; sizes?: string; type?: string }>
  shortcut?: string
  apple?: string | Array<{ url: string; sizes?: string; type?: string }>
  other?: Array<{ rel: string; url: string; sizes?: string; type?: string }>
}

export interface VerificationMetadata {
  google?: string
  yandex?: string
  yahoo?: string
  other?: Record<string, string>
}

export interface LayoutSlot {
  id: string
  name: string
  description: string
  type: 'children' | 'modal' | 'parallel' | 'intercepting'
  required: boolean
  defaultContent?: GeneratedComponent
}

export interface NextJSProject {
  id: string
  name: string
  description: string
  layouts: NextJSLayout[]
  pages: NextJSPage[]
  globalComponents: GeneratedComponent[]
  theme: ProjectTheme
  settings: ProjectSettings
  createdAt: Date
  updatedAt: Date
}

export interface ProjectTheme {
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    foreground: string
    muted: string
    border: string
  }
  typography: {
    fontFamily: string
    headingFont?: string
    fontSize: {
      xs: string
      sm: string
      base: string
      lg: string
      xl: string
      '2xl': string
      '3xl': string
      '4xl': string
    }
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
    '2xl': string
  }
  borderRadius: {
    sm: string
    md: string
    lg: string
    xl: string
  }
}

export interface ProjectSettings {
  typescript: boolean
  tailwindCSS: boolean
  appRouter: boolean
  srcDirectory: boolean
  eslint: boolean
  prettier: boolean
  husky: boolean
  commitlint: boolean
  storybook: boolean
  testing: {
    jest: boolean
    playwright: boolean
    cypress: boolean
  }
  deployment: {
    platform: 'vercel' | 'netlify' | 'aws' | 'custom'
    domain?: string
    environment: 'development' | 'staging' | 'production'
  }
}

export interface LayoutGenerationParams {
  name: string
  description: string
  type: 'root' | 'nested' | 'template' | 'group'
  features: string[]
  styling: 'modern' | 'minimal' | 'bold' | 'elegant' | 'playful'
  includeHeader: boolean
  includeFooter: boolean
  includeSidebar: boolean
  responsive: boolean
  accessibility: boolean
  seo: boolean
}

export interface PageGenerationParams {
  name: string
  description: string
  route: string
  type: 'static' | 'dynamic' | 'catch-all' | 'optional-catch-all'
  pageType: 'landing' | 'product' | 'blog' | 'dashboard' | 'auth' | 'error' | 'custom'
  features: string[]
  styling: 'modern' | 'minimal' | 'bold' | 'elegant' | 'playful'
  layout?: string
  seo: boolean
  generateStaticParams: boolean
  revalidate?: number | false
}

export interface RouteSegment {
  name: string
  type: 'static' | 'dynamic' | 'catch-all' | 'optional-catch-all'
  optional: boolean
}

export interface GeneratedNextJSStructure {
  layouts: NextJSLayout[]
  pages: NextJSPage[]
  components: GeneratedComponent[]
  routes: string[]
  fileStructure: FileStructureNode[]
}

export interface FileStructureNode {
  name: string
  type: 'file' | 'directory'
  path: string
  content?: string
  children?: FileStructureNode[]
}
