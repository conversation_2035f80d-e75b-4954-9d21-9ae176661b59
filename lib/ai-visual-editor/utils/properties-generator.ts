import { FieldConfig } from '@/lib/core/builders/components/properties-panel/custom-fields/types'
import { ComponentAnalysis, ComponentPropertiesConfig } from '../types'

export function generateAppearanceFields(analysis: ComponentAnalysis): FieldConfig[] {
  const fields: FieldConfig[] = []
  
  // Background color
  if (analysis.hasBackground) {
    fields.push({
      id: 'backgroundColor',
      type: 'color',
      label: 'Background Color',
      description: 'Set the background color',
      defaultValue: '#ffffff',
      alpha: true,
      gradient: true
    } as FieldConfig)
  }
  
  // Text color
  if (analysis.hasText) {
    fields.push({
      id: 'textColor',
      type: 'color',
      label: 'Text Color',
      description: 'Set the text color',
      defaultValue: '#000000'
    } as FieldConfig)
  }
  
  // Spacing
  fields.push({
    id: 'padding',
    type: 'spacing',
    label: 'Padding',
    description: 'Set internal spacing',
    defaultValue: { top: 16, right: 16, bottom: 16, left: 16 },
    sides: ['top', 'right', 'bottom', 'left'],
    linked: true,
    unit: 'px',
    min: 0,
    max: 100
  } as FieldConfig)
  
  fields.push({
    id: 'margin',
    type: 'spacing',
    label: 'Margin',
    description: 'Set external spacing',
    defaultValue: { top: 0, right: 0, bottom: 0, left: 0 },
    sides: ['top', 'right', 'bottom', 'left'],
    linked: true,
    unit: 'px',
    min: 0,
    max: 100
  } as FieldConfig)
  
  // Border
  if (analysis.canHaveBorder) {
    fields.push({
      id: 'border',
      type: 'border',
      label: 'Border',
      description: 'Configure border properties',
      defaultValue: { width: 0, style: 'solid', color: '#e5e7eb' },
      sides: ['top', 'right', 'bottom', 'left'],
      properties: ['width', 'style', 'color']
    } as FieldConfig)
  }
  
  // Border radius
  fields.push({
    id: 'borderRadius',
    type: 'range',
    label: 'Border Radius',
    description: 'Set corner roundness',
    defaultValue: 8,
    min: 0,
    max: 50,
    step: 1,
    unit: 'px',
    showValue: true
  } as FieldConfig)
  
  // Shadow
  fields.push({
    id: 'shadow',
    type: 'shadow',
    label: 'Shadow',
    description: 'Add shadow effects',
    defaultValue: 'none',
    multiple: false,
    presets: [
      { label: 'None', value: 'none' },
      { label: 'Small', value: '0 1px 3px rgba(0,0,0,0.12)' },
      { label: 'Medium', value: '0 4px 6px rgba(0,0,0,0.1)' },
      { label: 'Large', value: '0 10px 25px rgba(0,0,0,0.15)' }
    ]
  } as FieldConfig)
  
  return fields
}

export function generateContentFields(analysis: ComponentAnalysis): FieldConfig[] {
  const fields: FieldConfig[] = []
  
  // Title/Heading
  if (analysis.hasTitle) {
    fields.push({
      id: 'title',
      type: 'text',
      label: 'Title',
      description: 'Main heading text',
      placeholder: 'Enter title...',
      defaultValue: 'Your Title Here',
      validation: { required: true }
    } as FieldConfig)
    
    fields.push({
      id: 'titleSize',
      type: 'select',
      label: 'Title Size',
      description: 'Choose title size',
      defaultValue: 'text-2xl',
      options: [
        { label: 'Small', value: 'text-lg' },
        { label: 'Medium', value: 'text-xl' },
        { label: 'Large', value: 'text-2xl' },
        { label: 'Extra Large', value: 'text-3xl' },
        { label: 'Huge', value: 'text-4xl' }
      ]
    } as FieldConfig)
  }
  
  // Description
  if (analysis.hasDescription) {
    fields.push({
      id: 'description',
      type: 'richtext',
      label: 'Description',
      description: 'Detailed description text',
      defaultValue: 'Add your description here...',
      toolbar: ['bold', 'italic', 'link', 'list'],
      height: 150
    } as FieldConfig)
  }
  
  // Image
  if (analysis.hasImage) {
    fields.push({
      id: 'image',
      type: 'image',
      label: 'Image',
      description: 'Upload or select an image',
      defaultValue: null,
      accept: ['jpg', 'png', 'webp'],
      maxSize: 5000000, // 5MB
      crop: true,
      preview: true
    } as FieldConfig)
    
    fields.push({
      id: 'imageAlt',
      type: 'text',
      label: 'Image Alt Text',
      description: 'Alternative text for accessibility',
      placeholder: 'Describe the image...',
      defaultValue: '',
      showWhen: {
        field: 'image',
        operator: 'not-equals',
        value: null
      }
    } as FieldConfig)
  }
  
  // Icon
  if (analysis.hasIcon) {
    fields.push({
      id: 'icon',
      type: 'icon',
      label: 'Icon',
      description: 'Choose an icon',
      defaultValue: 'star',
      iconSet: 'lucide',
      searchable: true,
      size: 24
    } as FieldConfig)
  }
  
  // Links
  if (analysis.hasLinks) {
    fields.push({
      id: 'links',
      type: 'repeater',
      label: 'Links',
      description: 'Add navigation links',
      defaultValue: [],
      itemSchema: [
        {
          id: 'text',
          type: 'text',
          label: 'Link Text',
          defaultValue: 'Link',
          validation: { required: true }
        },
        {
          id: 'url',
          type: 'link',
          label: 'URL',
          defaultValue: '#',
          allowExternal: true,
          allowInternal: true
        }
      ],
      addLabel: 'Add Link',
      sortable: true
    } as FieldConfig)
  }
  
  return fields
}

export function generateBehaviorFields(analysis: ComponentAnalysis): FieldConfig[] {
  const fields: FieldConfig[] = []
  
  // Animation
  if (analysis.canAnimate) {
    fields.push({
      id: 'animation',
      type: 'select',
      label: 'Animation',
      description: 'Choose entrance animation',
      defaultValue: 'none',
      options: [
        { label: 'None', value: 'none' },
        { label: 'Fade In', value: 'fadeIn' },
        { label: 'Slide Up', value: 'slideUp' },
        { label: 'Slide Down', value: 'slideDown' },
        { label: 'Scale', value: 'scale' },
        { label: 'Bounce', value: 'bounce' }
      ]
    } as FieldConfig)
    
    fields.push({
      id: 'animationDuration',
      type: 'range',
      label: 'Animation Duration',
      description: 'Animation speed in milliseconds',
      defaultValue: 500,
      min: 100,
      max: 2000,
      step: 100,
      unit: 'ms',
      showWhen: {
        field: 'animation',
        operator: 'not-equals',
        value: 'none'
      }
    } as FieldConfig)
  }
  
  // Hover effects
  fields.push({
    id: 'hoverEffect',
    type: 'select',
    label: 'Hover Effect',
    description: 'Choose hover interaction',
    defaultValue: 'none',
    options: [
      { label: 'None', value: 'none' },
      { label: 'Scale', value: 'scale' },
      { label: 'Lift', value: 'lift' },
      { label: 'Glow', value: 'glow' },
      { label: 'Fade', value: 'fade' }
    ]
  } as FieldConfig)
  
  // Click action
  if (analysis.isClickable) {
    fields.push({
      id: 'clickAction',
      type: 'select',
      label: 'Click Action',
      description: 'What happens when clicked',
      defaultValue: 'none',
      options: [
        { label: 'None', value: 'none' },
        { label: 'Navigate', value: 'navigate' },
        { label: 'Open Modal', value: 'modal' },
        { label: 'Submit Form', value: 'submit' },
        { label: 'Scroll To', value: 'scroll' }
      ]
    } as FieldConfig)
    
    fields.push({
      id: 'clickTarget',
      type: 'text',
      label: 'Click Target',
      description: 'URL or element ID',
      placeholder: 'https://example.com or #section',
      defaultValue: '',
      showWhen: {
        field: 'clickAction',
        operator: 'not-equals',
        value: 'none'
      }
    } as FieldConfig)
  }
  
  return fields
}

export function generateDataFields(analysis: ComponentAnalysis): FieldConfig[] {
  const fields: FieldConfig[] = []
  
  // Data source
  if (analysis.needsData) {
    fields.push({
      id: 'dataSource',
      type: 'select',
      label: 'Data Source',
      description: 'Choose data source',
      defaultValue: 'static',
      options: [
        { label: 'Static', value: 'static' },
        { label: 'API', value: 'api' },
        { label: 'Database', value: 'database' },
        { label: 'CMS', value: 'cms' }
      ]
    } as FieldConfig)
    
    // API endpoint (conditional)
    fields.push({
      id: 'apiEndpoint',
      type: 'text',
      label: 'API Endpoint',
      description: 'API URL to fetch data',
      placeholder: 'https://api.example.com/data',
      defaultValue: '',
      showWhen: {
        field: 'dataSource',
        operator: 'equals',
        value: 'api'
      }
    } as FieldConfig)
    
    // Static data
    fields.push({
      id: 'staticData',
      type: 'code',
      label: 'Static Data',
      description: 'JSON data for the component',
      defaultValue: '[]',
      language: 'json',
      height: 200,
      showWhen: {
        field: 'dataSource',
        operator: 'equals',
        value: 'static'
      }
    } as FieldConfig)
  }
  
  return fields
}

export function generateLayoutFields(): FieldConfig[] {
  return [
    {
      id: 'width',
      type: 'select',
      label: 'Width',
      description: 'Component width',
      defaultValue: 'w-full',
      options: [
        { label: 'Auto', value: 'w-auto' },
        { label: 'Full', value: 'w-full' },
        { label: 'Half', value: 'w-1/2' },
        { label: 'Third', value: 'w-1/3' },
        { label: 'Quarter', value: 'w-1/4' },
        { label: 'Fixed 300px', value: 'w-[300px]' },
        { label: 'Fixed 500px', value: 'w-[500px]' }
      ]
    } as FieldConfig,
    
    {
      id: 'height',
      type: 'select',
      label: 'Height',
      description: 'Component height',
      defaultValue: 'h-auto',
      options: [
        { label: 'Auto', value: 'h-auto' },
        { label: 'Screen', value: 'h-screen' },
        { label: '200px', value: 'h-[200px]' },
        { label: '400px', value: 'h-[400px]' },
        { label: '600px', value: 'h-[600px]' }
      ]
    } as FieldConfig,
    
    {
      id: 'alignment',
      type: 'select',
      label: 'Alignment',
      description: 'Text and content alignment',
      defaultValue: 'text-left',
      options: [
        { label: 'Left', value: 'text-left' },
        { label: 'Center', value: 'text-center' },
        { label: 'Right', value: 'text-right' },
        { label: 'Justify', value: 'text-justify' }
      ]
    } as FieldConfig,
    
    {
      id: 'display',
      type: 'select',
      label: 'Display',
      description: 'CSS display property',
      defaultValue: 'block',
      options: [
        { label: 'Block', value: 'block' },
        { label: 'Flex', value: 'flex' },
        { label: 'Grid', value: 'grid' },
        { label: 'Inline', value: 'inline' },
        { label: 'Inline Block', value: 'inline-block' }
      ]
    } as FieldConfig
  ]
}

export function generatePropertiesSchema(analysis: ComponentAnalysis): ComponentPropertiesConfig {
  return {
    appearance: generateAppearanceFields(analysis),
    content: generateContentFields(analysis),
    behavior: generateBehaviorFields(analysis),
    data: generateDataFields(analysis),
    layout: generateLayoutFields()
  }
}

export function generateDefaultValues(propertiesConfig: ComponentPropertiesConfig): Record<string, any> {
  const defaultValues: Record<string, any> = {}
  
  Object.values(propertiesConfig).forEach(fields => {
    fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        defaultValues[field.id] = field.defaultValue
      }
    })
  })
  
  return defaultValues
}
