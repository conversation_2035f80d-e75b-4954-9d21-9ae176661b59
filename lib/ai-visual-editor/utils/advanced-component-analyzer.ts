import { ComponentAnalysis } from '../types'

interface AdvancedComponentAnalysis extends ComponentAnalysis {
  complexity: 'simple' | 'moderate' | 'complex'
  interactivity: 'static' | 'interactive' | 'dynamic'
  dataRequirements: 'none' | 'static' | 'api' | 'realtime'
  accessibilityFeatures: string[]
  performanceConsiderations: string[]
  suggestedOptimizations: string[]
  semanticStructure: SemanticElement[]
  dependencies: string[]
  estimatedRenderTime: number
}

interface SemanticElement {
  type: 'header' | 'main' | 'section' | 'article' | 'aside' | 'nav' | 'footer'
  role?: string
  ariaLabel?: string
  children: SemanticElement[]
}

export function analyzeComponentAdvanced(componentCode: string): AdvancedComponentAnalysis {
  const basicAnalysis = analyzeComponentStructure(componentCode)
  const ast = parseComponentAST(componentCode)
  
  return {
    ...basicAnalysis,
    complexity: determineComplexity(ast),
    interactivity: determineInteractivity(ast),
    dataRequirements: determineDataRequirements(ast),
    accessibilityFeatures: extractAccessibilityFeatures(ast),
    performanceConsiderations: analyzePerformance(ast),
    suggestedOptimizations: generateOptimizations(ast),
    semanticStructure: extractSemanticStructure(ast),
    dependencies: extractDependencies(componentCode),
    estimatedRenderTime: estimateRenderTime(ast)
  }
}

function parseComponentAST(code: string): ComponentAST {
  // Simplified AST parsing - in production, use @babel/parser or similar
  const ast: ComponentAST = {
    type: 'Component',
    name: extractComponentName(code),
    props: extractProps(code),
    hooks: extractHooks(code),
    jsx: extractJSXElements(code),
    imports: extractImports(code),
    exports: extractExports(code)
  }
  
  return ast
}

function determineComplexity(ast: ComponentAST): 'simple' | 'moderate' | 'complex' {
  let complexityScore = 0
  
  // Count JSX elements
  complexityScore += ast.jsx.length * 2
  
  // Count hooks
  complexityScore += ast.hooks.length * 5
  
  // Count conditional rendering
  const conditionals = ast.jsx.filter(el => el.conditional).length
  complexityScore += conditionals * 3
  
  // Count loops/maps
  const loops = ast.jsx.filter(el => el.isLoop).length
  complexityScore += loops * 4
  
  if (complexityScore < 10) return 'simple'
  if (complexityScore < 25) return 'moderate'
  return 'complex'
}

function determineInteractivity(ast: ComponentAST): 'static' | 'interactive' | 'dynamic' {
  const hasEventHandlers = ast.jsx.some(el => 
    el.props.some(prop => prop.name.startsWith('on'))
  )
  
  const hasState = ast.hooks.some(hook => 
    hook.type === 'useState' || hook.type === 'useReducer'
  )
  
  const hasEffects = ast.hooks.some(hook => 
    hook.type === 'useEffect' || hook.type === 'useLayoutEffect'
  )
  
  if (hasEffects || hasState) return 'dynamic'
  if (hasEventHandlers) return 'interactive'
  return 'static'
}

function determineDataRequirements(ast: ComponentAST): 'none' | 'static' | 'api' | 'realtime' {
  const hasFetch = ast.jsx.some(el => 
    el.content?.includes('fetch') || el.content?.includes('axios')
  )
  
  const hasWebSocket = ast.jsx.some(el => 
    el.content?.includes('WebSocket') || el.content?.includes('socket')
  )
  
  const hasStaticData = ast.jsx.some(el => 
    el.props.some(prop => prop.value && typeof prop.value === 'object')
  )
  
  if (hasWebSocket) return 'realtime'
  if (hasFetch) return 'api'
  if (hasStaticData) return 'static'
  return 'none'
}

function extractAccessibilityFeatures(ast: ComponentAST): string[] {
  const features: string[] = []
  
  ast.jsx.forEach(element => {
    element.props.forEach(prop => {
      if (prop.name.startsWith('aria-')) {
        features.push(`ARIA: ${prop.name}`)
      }
      if (prop.name === 'role') {
        features.push(`Role: ${prop.value}`)
      }
      if (prop.name === 'alt' && element.type === 'img') {
        features.push('Image alt text')
      }
      if (prop.name === 'tabIndex') {
        features.push('Keyboard navigation')
      }
    })
    
    // Check for semantic HTML elements
    const semanticElements = ['header', 'main', 'section', 'article', 'aside', 'nav', 'footer']
    if (semanticElements.includes(element.type)) {
      features.push(`Semantic HTML: ${element.type}`)
    }
  })
  
  return [...new Set(features)]
}

function analyzePerformance(ast: ComponentAST): string[] {
  const considerations: string[] = []
  
  // Check for potential performance issues
  const hasInlineObjects = ast.jsx.some(el => 
    el.props.some(prop => prop.value && typeof prop.value === 'object')
  )
  if (hasInlineObjects) {
    considerations.push('Inline objects in props may cause unnecessary re-renders')
  }
  
  const hasInlineFunctions = ast.jsx.some(el => 
    el.props.some(prop => prop.name.startsWith('on') && prop.value?.includes('=>'))
  )
  if (hasInlineFunctions) {
    considerations.push('Inline functions may cause unnecessary re-renders')
  }
  
  const hasLargeArrays = ast.jsx.some(el => el.isLoop)
  if (hasLargeArrays) {
    considerations.push('Large lists should use virtualization for better performance')
  }
  
  const hasImages = ast.jsx.some(el => el.type === 'img')
  if (hasImages) {
    considerations.push('Images should be optimized and use lazy loading')
  }
  
  return considerations
}

function generateOptimizations(ast: ComponentAST): string[] {
  const optimizations: string[] = []
  
  // Suggest React.memo for components without props
  if (ast.props.length === 0) {
    optimizations.push('Wrap component with React.memo for better performance')
  }
  
  // Suggest useMemo for expensive calculations
  const hasComplexCalculations = ast.jsx.some(el => 
    el.content?.includes('filter') || el.content?.includes('map') || el.content?.includes('reduce')
  )
  if (hasComplexCalculations) {
    optimizations.push('Use useMemo for expensive calculations')
  }
  
  // Suggest useCallback for event handlers
  const hasEventHandlers = ast.jsx.some(el => 
    el.props.some(prop => prop.name.startsWith('on'))
  )
  if (hasEventHandlers) {
    optimizations.push('Use useCallback for event handlers to prevent re-renders')
  }
  
  // Suggest code splitting for large components
  if (ast.jsx.length > 20) {
    optimizations.push('Consider splitting into smaller components')
  }
  
  return optimizations
}

function extractSemanticStructure(ast: ComponentAST): SemanticElement[] {
  const semanticElements: SemanticElement[] = []
  
  ast.jsx.forEach(element => {
    const semanticTypes = ['header', 'main', 'section', 'article', 'aside', 'nav', 'footer']
    if (semanticTypes.includes(element.type)) {
      const ariaLabel = element.props.find(p => p.name === 'aria-label')?.value
      const role = element.props.find(p => p.name === 'role')?.value
      
      semanticElements.push({
        type: element.type as any,
        role,
        ariaLabel,
        children: [] // Simplified - would need recursive parsing
      })
    }
  })
  
  return semanticElements
}

function extractDependencies(code: string): string[] {
  const dependencies: string[] = []
  const importMatches = code.match(/import\s+.*?from\s+['"]([^'"]+)['"]/g)
  
  if (importMatches) {
    importMatches.forEach(match => {
      const moduleMatch = match.match(/from\s+['"]([^'"]+)['"]/)
      if (moduleMatch) {
        dependencies.push(moduleMatch[1])
      }
    })
  }
  
  return dependencies
}

function estimateRenderTime(ast: ComponentAST): number {
  let estimatedTime = 1 // Base time in ms
  
  // Add time for each JSX element
  estimatedTime += ast.jsx.length * 0.1
  
  // Add time for hooks
  estimatedTime += ast.hooks.length * 0.5
  
  // Add time for loops
  const loops = ast.jsx.filter(el => el.isLoop).length
  estimatedTime += loops * 2
  
  // Add time for images
  const images = ast.jsx.filter(el => el.type === 'img').length
  estimatedTime += images * 5
  
  return Math.round(estimatedTime * 100) / 100
}

// Helper functions for AST parsing
function extractComponentName(code: string): string {
  const match = code.match(/(?:function|const)\s+(\w+)/)
  return match ? match[1] : 'UnknownComponent'
}

function extractProps(code: string): PropDefinition[] {
  // Simplified prop extraction
  const propsMatch = code.match(/\{\s*([^}]+)\s*\}:\s*\w+Props/)
  if (!propsMatch) return []
  
  const propsString = propsMatch[1]
  const propNames = propsString.split(',').map(p => p.trim())
  
  return propNames.map(name => ({
    name: name.replace(/[{}]/g, ''),
    type: 'unknown',
    optional: false
  }))
}

function extractHooks(code: string): HookUsage[] {
  const hooks: HookUsage[] = []
  const hookPattern = /use\w+\(/g
  const matches = code.match(hookPattern)
  
  if (matches) {
    matches.forEach(match => {
      const hookName = match.slice(0, -1) // Remove the '('
      hooks.push({
        type: hookName,
        dependencies: []
      })
    })
  }
  
  return hooks
}

function extractJSXElements(code: string): JSXElement[] {
  // Simplified JSX extraction
  const elements: JSXElement[] = []
  const elementPattern = /<(\w+)([^>]*)>/g
  let match
  
  while ((match = elementPattern.exec(code)) !== null) {
    const [, tagName, propsString] = match
    const props = parseProps(propsString)
    
    elements.push({
      type: tagName,
      props,
      children: [],
      conditional: propsString.includes('&&') || propsString.includes('?'),
      isLoop: propsString.includes('.map('),
      content: ''
    })
  }
  
  return elements
}

function parseProps(propsString: string): PropUsage[] {
  const props: PropUsage[] = []
  const propPattern = /(\w+)=\{?([^}\s]+)\}?/g
  let match
  
  while ((match = propPattern.exec(propsString)) !== null) {
    const [, name, value] = match
    props.push({
      name,
      value: value.replace(/['"]/g, '')
    })
  }
  
  return props
}

function extractImports(code: string): string[] {
  const imports: string[] = []
  const importPattern = /import\s+.*?from\s+['"]([^'"]+)['"]/g
  let match
  
  while ((match = importPattern.exec(code)) !== null) {
    imports.push(match[1])
  }
  
  return imports
}

function extractExports(code: string): string[] {
  const exports: string[] = []
  const exportPattern = /export\s+(?:default\s+)?(\w+)/g
  let match
  
  while ((match = exportPattern.exec(code)) !== null) {
    exports.push(match[1])
  }
  
  return exports
}

// Type definitions for AST
interface ComponentAST {
  type: 'Component'
  name: string
  props: PropDefinition[]
  hooks: HookUsage[]
  jsx: JSXElement[]
  imports: string[]
  exports: string[]
}

interface PropDefinition {
  name: string
  type: string
  optional: boolean
}

interface HookUsage {
  type: string
  dependencies: string[]
}

interface JSXElement {
  type: string
  props: PropUsage[]
  children: JSXElement[]
  conditional: boolean
  isLoop: boolean
  content: string
}

interface PropUsage {
  name: string
  value: string
}

// Re-export basic analysis for backward compatibility
function analyzeComponentStructure(componentCode: string): ComponentAnalysis {
  const code = componentCode.toLowerCase()
  
  const hasBackground = /background|bg-/.test(code)
  const hasText = /text|title|heading|paragraph|p>|h[1-6]>/.test(code)
  const hasTitle = /title|heading|h[1-6]>/.test(code)
  const hasDescription = /description|paragraph|p>/.test(code)
  const hasImage = /img|image|picture/.test(code)
  const hasIcon = /icon|svg|lucide/.test(code)
  const hasLinks = /link|href|anchor|a>/.test(code)
  const canHaveBorder = /border|outline|ring/.test(code) || hasBackground
  const canAnimate = /transition|animate|motion/.test(code)
  const isClickable = /onclick|button|link|href/.test(code)
  const needsData = /map\(|\.map|foreach|data|items|list/.test(code)
  
  const features = []
  if (hasTitle) features.push('title')
  if (hasDescription) features.push('description')
  if (hasImage) features.push('image')
  if (hasIcon) features.push('icon')
  if (hasLinks) features.push('links')
  if (isClickable) features.push('interactive')
  if (needsData) features.push('data-driven')
  
  const summary = `Component with ${features.join(', ')} features`
  
  return {
    hasBackground,
    hasText,
    hasTitle,
    hasDescription,
    hasImage,
    hasIcon,
    hasLinks,
    canHaveBorder,
    canAnimate,
    isClickable,
    needsData,
    summary
  }
}
