import { ComponentAnalysis } from '../types'

export function analyzeComponentStructure(componentCode: string): ComponentAnalysis {
  const code = componentCode.toLowerCase()
  
  // Analyze component structure
  const hasBackground = /background|bg-/.test(code)
  const hasText = /text|title|heading|paragraph|p>|h[1-6]>/.test(code)
  const hasTitle = /title|heading|h[1-6]>/.test(code)
  const hasDescription = /description|paragraph|p>/.test(code)
  const hasImage = /img|image|picture/.test(code)
  const hasIcon = /icon|svg|lucide/.test(code)
  const hasLinks = /link|href|anchor|a>/.test(code)
  const canHaveBorder = /border|outline|ring/.test(code) || hasBackground
  const canAnimate = /transition|animate|motion/.test(code)
  const isClickable = /onclick|button|link|href/.test(code)
  const needsData = /map\(|\.map|foreach|data|items|list/.test(code)
  
  // Generate summary
  const features = []
  if (hasTitle) features.push('title')
  if (hasDescription) features.push('description')
  if (hasImage) features.push('image')
  if (hasIcon) features.push('icon')
  if (hasLinks) features.push('links')
  if (isClickable) features.push('interactive')
  if (needsData) features.push('data-driven')
  
  const summary = `Component with ${features.join(', ')} features`
  
  return {
    hasBackground,
    hasText,
    hasTitle,
    hasDescription,
    hasImage,
    hasIcon,
    hasLinks,
    canHaveBorder,
    canAnimate,
    isClickable,
    needsData,
    summary
  }
}

export function extractPropsFromComponent(componentCode: string): Record<string, any> {
  const props: Record<string, any> = {}
  
  // Extract interface/type definitions
  const interfaceMatch = componentCode.match(/interface\s+\w+Props\s*{([^}]+)}/s)
  if (interfaceMatch) {
    const propsContent = interfaceMatch[1]
    const propMatches = propsContent.match(/(\w+)(\?)?:\s*([^;\n]+)/g)
    
    if (propMatches) {
      propMatches.forEach(match => {
        const [, name, optional, type] = match.match(/(\w+)(\?)?:\s*(.+)/) || []
        if (name && type) {
          props[name] = {
            type: type.trim(),
            optional: !!optional,
            defaultValue: getDefaultValueForType(type.trim())
          }
        }
      })
    }
  }
  
  return props
}

function getDefaultValueForType(type: string): any {
  if (type.includes('string')) return ''
  if (type.includes('number')) return 0
  if (type.includes('boolean')) return false
  if (type.includes('[]') || type.includes('Array')) return []
  if (type.includes('{}') || type.includes('object')) return {}
  return null
}

export function generateComponentId(): string {
  return `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export function validateComponentCode(code: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // Basic syntax checks
  if (!code.includes('export') && !code.includes('function') && !code.includes('const')) {
    errors.push('Component must export a function or const')
  }
  
  if (!code.includes('return')) {
    errors.push('Component must return JSX')
  }
  
  // Check for balanced brackets
  const openBraces = (code.match(/{/g) || []).length
  const closeBraces = (code.match(/}/g) || []).length
  if (openBraces !== closeBraces) {
    errors.push('Unbalanced braces in component code')
  }
  
  const openParens = (code.match(/\(/g) || []).length
  const closeParens = (code.match(/\)/g) || []).length
  if (openParens !== closeParens) {
    errors.push('Unbalanced parentheses in component code')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export function sanitizeComponentCode(code: string): string {
  // Remove potentially dangerous code
  const dangerous = [
    /eval\s*\(/g,
    /Function\s*\(/g,
    /document\./g,
    /window\./g,
    /localStorage/g,
    /sessionStorage/g,
    /fetch\s*\(/g,
    /XMLHttpRequest/g,
    /import\s*\(/g
  ]
  
  let sanitized = code
  dangerous.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '// REMOVED_FOR_SECURITY')
  })
  
  return sanitized
}

export function extractImportsFromComponent(code: string): string[] {
  const imports: string[] = []
  const importMatches = code.match(/import\s+.*?from\s+['"][^'"]+['"]/g)
  
  if (importMatches) {
    importMatches.forEach(importStatement => {
      const moduleMatch = importStatement.match(/from\s+['"]([^'"]+)['"]/)
      if (moduleMatch) {
        imports.push(moduleMatch[1])
      }
    })
  }
  
  return imports
}
