import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { act } from 'react-dom/test-utils'
import { AIVisualEditor } from '../components/ai-visual-editor'
import { useEditorStore } from '../stores/editor-store'
import { GeneratedComponent } from '../types'

// Mock the AI SDK
jest.mock('@ai-sdk/react', () => ({
  useChat: jest.fn(() => ({
    messages: [],
    input: '',
    handleInputChange: jest.fn(),
    handleSubmit: jest.fn(),
    isLoading: false
  }))
}))

// Mock toast notifications
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn()
  }
}))

const mockComponent: GeneratedComponent = {
  id: 'test-component-1',
  name: 'Test Hero Component',
  description: 'A test hero component',
  category: 'hero',
  jsx: '<div className="hero bg-blue-500 text-white p-8"><h1>Test Hero</h1></div>',
  props: {},
  propertiesConfig: {
    appearance: [
      {
        id: 'backgroundColor',
        type: 'color',
        label: 'Background Color',
        defaultValue: '#3b82f6'
      }
    ],
    content: [
      {
        id: 'title',
        type: 'text',
        label: 'Title',
        defaultValue: 'Test Hero'
      }
    ],
    behavior: [],
    data: [],
    layout: []
  },
  defaultValues: {
    backgroundColor: '#3b82f6',
    title: 'Test Hero'
  },
  createdAt: new Date(),
  updatedAt: new Date()
}

describe('AI Visual Editor Integration Tests', () => {
  beforeEach(() => {
    // Reset store before each test
    useEditorStore.setState({
      components: [],
      selectedComponentId: null,
      propertyValues: {},
      isGenerating: false,
      previewMode: 'desktop',
      showComponentTree: true,
      chatMessages: [],
      isAIResponding: false
    })
  })

  describe('Component Management', () => {
    test('should add component to editor', async () => {
      const { addComponent } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
      })
      
      const { components, selectedComponentId } = useEditorStore.getState()
      
      expect(components).toHaveLength(1)
      expect(components[0]).toEqual(mockComponent)
      expect(selectedComponentId).toBe(mockComponent.id)
    })

    test('should update component properties', async () => {
      const { addComponent, updatePropertyValue } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
        updatePropertyValue(mockComponent.id, 'title', 'Updated Title')
      })
      
      const { propertyValues } = useEditorStore.getState()
      
      expect(propertyValues[mockComponent.id].title).toBe('Updated Title')
    })

    test('should delete component', async () => {
      const { addComponent, deleteComponent } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
        deleteComponent(mockComponent.id)
      })
      
      const { components, selectedComponentId } = useEditorStore.getState()
      
      expect(components).toHaveLength(0)
      expect(selectedComponentId).toBeNull()
    })
  })

  describe('Properties Panel', () => {
    test('should render properties for selected component', async () => {
      const { addComponent } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
      })

      render(<AIVisualEditor />)
      
      // Check if properties panel shows component properties
      expect(screen.getByText('Properties')).toBeInTheDocument()
      expect(screen.getByText('Test Hero Component')).toBeInTheDocument()
    })

    test('should show empty state when no component selected', async () => {
      render(<AIVisualEditor />)
      
      expect(screen.getByText('No Component Selected')).toBeInTheDocument()
    })
  })

  describe('Live Preview', () => {
    test('should render component in preview', async () => {
      const { addComponent } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
      })

      render(<AIVisualEditor />)
      
      // Check if preview shows the component
      expect(screen.getByText('Live Preview')).toBeInTheDocument()
    })

    test('should switch preview modes', async () => {
      const { setPreviewMode } = useEditorStore.getState()
      
      act(() => {
        setPreviewMode('tablet')
      })
      
      const { previewMode } = useEditorStore.getState()
      expect(previewMode).toBe('tablet')
    })
  })

  describe('Component Tree', () => {
    test('should display components in tree', async () => {
      const { addComponent } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
      })

      render(<AIVisualEditor />)
      
      // Switch to components tab
      const componentsTab = screen.getByText('Components')
      fireEvent.click(componentsTab)
      
      expect(screen.getByText('Test Hero Component')).toBeInTheDocument()
    })

    test('should filter components by search', async () => {
      const { addComponent } = useEditorStore.getState()
      
      const component2 = {
        ...mockComponent,
        id: 'test-component-2',
        name: 'Test Card Component',
        category: 'card' as const
      }
      
      act(() => {
        addComponent(mockComponent)
        addComponent(component2)
      })

      render(<AIVisualEditor />)
      
      // Switch to components tab
      const componentsTab = screen.getByText('Components')
      fireEvent.click(componentsTab)
      
      // Search for "hero"
      const searchInput = screen.getByPlaceholderText('Search components...')
      fireEvent.change(searchInput, { target: { value: 'hero' } })
      
      expect(screen.getByText('Test Hero Component')).toBeInTheDocument()
      expect(screen.queryByText('Test Card Component')).not.toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    test('should handle component rendering errors gracefully', async () => {
      const invalidComponent = {
        ...mockComponent,
        jsx: '<div><invalid-jsx></div>' // Invalid JSX
      }
      
      const { addComponent } = useEditorStore.getState()
      
      act(() => {
        addComponent(invalidComponent)
      })

      render(<AIVisualEditor />)
      
      // Should not crash and show error state
      expect(screen.getByText('Live Preview')).toBeInTheDocument()
    })

    test('should validate property values', async () => {
      const { addComponent, updatePropertyValue } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
        // Try to set invalid color value
        updatePropertyValue(mockComponent.id, 'backgroundColor', 'invalid-color')
      })
      
      const { propertyValues } = useEditorStore.getState()
      
      // Should handle invalid values gracefully
      expect(propertyValues[mockComponent.id]).toBeDefined()
    })
  })

  describe('Performance', () => {
    test('should handle multiple components efficiently', async () => {
      const { addComponent } = useEditorStore.getState()
      
      const startTime = performance.now()
      
      // Add multiple components
      act(() => {
        for (let i = 0; i < 50; i++) {
          addComponent({
            ...mockComponent,
            id: `component-${i}`,
            name: `Component ${i}`
          })
        }
      })
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // Should complete within reasonable time
      expect(duration).toBeLessThan(1000) // 1 second
      
      const { components } = useEditorStore.getState()
      expect(components).toHaveLength(50)
    })

    test('should debounce property updates', async () => {
      const { addComponent, updatePropertyValue } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
      })

      // Simulate rapid property updates
      act(() => {
        for (let i = 0; i < 10; i++) {
          updatePropertyValue(mockComponent.id, 'title', `Title ${i}`)
        }
      })
      
      const { propertyValues } = useEditorStore.getState()
      
      // Should have the final value
      expect(propertyValues[mockComponent.id].title).toBe('Title 9')
    })
  })

  describe('Accessibility', () => {
    test('should have proper ARIA labels', async () => {
      render(<AIVisualEditor />)
      
      // Check for important accessibility features
      expect(screen.getByRole('main')).toBeInTheDocument()
      expect(screen.getByLabelText(/AI Assistant/i)).toBeInTheDocument()
    })

    test('should support keyboard navigation', async () => {
      const { addComponent } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
      })

      render(<AIVisualEditor />)
      
      // Test tab navigation
      const firstFocusable = screen.getByRole('button', { name: /reset/i })
      firstFocusable.focus()
      
      expect(document.activeElement).toBe(firstFocusable)
    })
  })

  describe('Data Persistence', () => {
    test('should persist state to localStorage', async () => {
      const { addComponent } = useEditorStore.getState()
      
      act(() => {
        addComponent(mockComponent)
      })
      
      // Check if state is persisted
      const persistedState = localStorage.getItem('ai-visual-editor-store')
      expect(persistedState).toBeTruthy()
      
      const parsed = JSON.parse(persistedState!)
      expect(parsed.state.components).toHaveLength(1)
    })

    test('should restore state from localStorage', async () => {
      // Set up persisted state
      const persistedState = {
        state: {
          components: [mockComponent],
          propertyValues: { [mockComponent.id]: mockComponent.defaultValues }
        },
        version: 0
      }
      
      localStorage.setItem('ai-visual-editor-store', JSON.stringify(persistedState))
      
      // Create new store instance (simulating page reload)
      const { components } = useEditorStore.getState()
      
      expect(components).toHaveLength(1)
      expect(components[0].name).toBe('Test Hero Component')
    })
  })
})
