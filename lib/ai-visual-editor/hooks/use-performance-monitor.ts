'use client'

import { useEffect, useRef, useState, useCallback } from 'react'

interface PerformanceMetrics {
  renderTime: number
  memoryUsage: number
  componentCount: number
  reRenderCount: number
  lastUpdate: Date
}

interface PerformanceAlert {
  type: 'warning' | 'error'
  message: string
  suggestion: string
  timestamp: Date
}

export function usePerformanceMonitor(componentId?: string) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    componentCount: 0,
    reRenderCount: 0,
    lastUpdate: new Date()
  })
  
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([])
  const renderStartTime = useRef<number>(0)
  const renderCount = useRef<number>(0)
  const memoryCheckInterval = useRef<NodeJS.Timeout>()

  // Start render timing
  const startRenderTiming = useCallback(() => {
    renderStartTime.current = performance.now()
  }, [])

  // End render timing
  const endRenderTiming = useCallback(() => {
    const renderTime = performance.now() - renderStartTime.current
    renderCount.current += 1
    
    setMetrics(prev => ({
      ...prev,
      renderTime,
      reRenderCount: renderCount.current,
      lastUpdate: new Date()
    }))

    // Check for performance issues
    if (renderTime > 16) { // 60fps threshold
      setAlerts(prev => [...prev, {
        type: 'warning',
        message: `Slow render detected: ${renderTime.toFixed(2)}ms`,
        suggestion: 'Consider memoizing components or reducing complexity',
        timestamp: new Date()
      }].slice(-10)) // Keep last 10 alerts
    }

    if (renderCount.current > 100) {
      setAlerts(prev => [...prev, {
        type: 'warning',
        message: `High re-render count: ${renderCount.current}`,
        suggestion: 'Check for unnecessary state updates or prop changes',
        timestamp: new Date()
      }].slice(-10))
    }
  }, [])

  // Monitor memory usage
  useEffect(() => {
    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        const memoryUsage = memory.usedJSHeapSize / 1024 / 1024 // MB
        
        setMetrics(prev => ({
          ...prev,
          memoryUsage,
          lastUpdate: new Date()
        }))

        // Alert for high memory usage
        if (memoryUsage > 100) { // 100MB threshold
          setAlerts(prev => [...prev, {
            type: 'error',
            message: `High memory usage: ${memoryUsage.toFixed(2)}MB`,
            suggestion: 'Consider reducing component complexity or implementing virtualization',
            timestamp: new Date()
          }].slice(-10))
        }
      }
    }

    memoryCheckInterval.current = setInterval(checkMemory, 5000) // Check every 5 seconds
    checkMemory() // Initial check

    return () => {
      if (memoryCheckInterval.current) {
        clearInterval(memoryCheckInterval.current)
      }
    }
  }, [])

  // Clear alerts
  const clearAlerts = useCallback(() => {
    setAlerts([])
  }, [])

  // Get performance score
  const getPerformanceScore = useCallback((): number => {
    let score = 100
    
    // Deduct for slow renders
    if (metrics.renderTime > 16) score -= 20
    if (metrics.renderTime > 33) score -= 30
    
    // Deduct for high memory usage
    if (metrics.memoryUsage > 50) score -= 15
    if (metrics.memoryUsage > 100) score -= 35
    
    // Deduct for excessive re-renders
    if (metrics.reRenderCount > 50) score -= 10
    if (metrics.reRenderCount > 100) score -= 25
    
    return Math.max(0, score)
  }, [metrics])

  return {
    metrics,
    alerts,
    startRenderTiming,
    endRenderTiming,
    clearAlerts,
    performanceScore: getPerformanceScore()
  }
}

// Hook for component-level performance monitoring
export function useComponentPerformance(componentName: string) {
  const renderCount = useRef(0)
  const lastRenderTime = useRef(0)
  
  useEffect(() => {
    const start = performance.now()
    
    return () => {
      const end = performance.now()
      const renderTime = end - start
      renderCount.current += 1
      lastRenderTime.current = renderTime
      
      // Log performance data in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${componentName}: ${renderTime.toFixed(2)}ms (render #${renderCount.current})`)
      }
    }
  })

  return {
    renderCount: renderCount.current,
    lastRenderTime: lastRenderTime.current
  }
}

// Hook for debounced updates to prevent excessive re-renders
export function useDebouncedUpdate<T>(value: T, delay: number = 300): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Hook for virtualized rendering of large lists
export function useVirtualization(
  items: any[],
  itemHeight: number,
  containerHeight: number
) {
  const [scrollTop, setScrollTop] = useState(0)
  
  const visibleStart = Math.floor(scrollTop / itemHeight)
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  )
  
  const visibleItems = items.slice(visibleStart, visibleEnd)
  const totalHeight = items.length * itemHeight
  const offsetY = visibleStart * itemHeight

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    visibleStart,
    visibleEnd
  }
}

// Hook for lazy loading components
export function useLazyComponent<T>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  const [Component, setComponent] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let mounted = true

    importFn()
      .then((module) => {
        if (mounted) {
          setComponent(module.default)
          setLoading(false)
        }
      })
      .catch((err) => {
        if (mounted) {
          setError(err)
          setLoading(false)
        }
      })

    return () => {
      mounted = false
    }
  }, [importFn])

  return { Component, loading, error }
}

// Performance monitoring context
export interface PerformanceContextValue {
  isMonitoring: boolean
  startMonitoring: () => void
  stopMonitoring: () => void
  getReport: () => PerformanceReport
}

export interface PerformanceReport {
  totalComponents: number
  averageRenderTime: number
  memoryUsage: number
  slowComponents: string[]
  recommendations: string[]
}

// Global performance utilities
export const PerformanceUtils = {
  // Measure function execution time
  measureTime: <T extends (...args: any[]) => any>(
    fn: T,
    label?: string
  ): T => {
    return ((...args: any[]) => {
      const start = performance.now()
      const result = fn(...args)
      const end = performance.now()
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${label || fn.name}: ${(end - start).toFixed(2)}ms`)
      }
      
      return result
    }) as T
  },

  // Check if component should update
  shouldComponentUpdate: (
    prevProps: Record<string, any>,
    nextProps: Record<string, any>
  ): boolean => {
    const prevKeys = Object.keys(prevProps)
    const nextKeys = Object.keys(nextProps)
    
    if (prevKeys.length !== nextKeys.length) return true
    
    return prevKeys.some(key => prevProps[key] !== nextProps[key])
  },

  // Optimize large object comparisons
  deepEqual: (obj1: any, obj2: any): boolean => {
    if (obj1 === obj2) return true
    
    if (obj1 == null || obj2 == null) return false
    if (typeof obj1 !== typeof obj2) return false
    
    if (typeof obj1 !== 'object') return obj1 === obj2
    
    const keys1 = Object.keys(obj1)
    const keys2 = Object.keys(obj2)
    
    if (keys1.length !== keys2.length) return false
    
    return keys1.every(key => PerformanceUtils.deepEqual(obj1[key], obj2[key]))
  }
}
