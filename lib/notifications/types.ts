// Email and Notification Types

export interface EmailTemplate {
  id: string
  name: string
  subject: string
  htmlContent: string
  textContent: string
  variables: EmailVariable[]
  category: EmailCategory
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface EmailVariable {
  name: string
  description: string
  type: 'string' | 'number' | 'date' | 'currency' | 'url'
  required: boolean
  defaultValue?: string
}

export enum EmailCategory {
  ORDER = 'order',
  PAYMENT = 'payment',
  SHIPPING = 'shipping',
  CUSTOMER = 'customer',
  INVENTORY = 'inventory',
  MARKETING = 'marketing',
  SYSTEM = 'system'
}

export interface EmailRequest {
  to: string | string[]
  cc?: string[]
  bcc?: string[]
  templateId: string
  variables: Record<string, any>
  attachments?: EmailAttachment[]
  priority?: EmailPriority
  scheduledAt?: Date
  tags?: string[]
}

export interface EmailAttachment {
  filename: string
  content: string // Base64 encoded
  contentType: string
  size: number
}

export enum EmailPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface EmailLog {
  id: string
  to: string[]
  cc?: string[]
  bcc?: string[]
  subject: string
  templateId: string
  variables: Record<string, any>
  status: EmailStatus
  provider: EmailProvider
  messageId?: string
  error?: string
  sentAt?: Date
  deliveredAt?: Date
  openedAt?: Date
  clickedAt?: Date
  bouncedAt?: Date
  createdAt: Date
}

export enum EmailStatus {
  PENDING = 'pending',
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  BOUNCED = 'bounced',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum EmailProvider {
  SMTP = 'smtp',
  SENDGRID = 'sendgrid',
  MAILGUN = 'mailgun',
  SES = 'ses',
  POSTMARK = 'postmark'
}

// SMS Notifications
export interface SMSRequest {
  to: string | string[]
  message: string
  templateId?: string
  variables?: Record<string, any>
  priority?: SMSPriority
  scheduledAt?: Date
}

export enum SMSPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface SMSLog {
  id: string
  to: string
  message: string
  templateId?: string
  status: SMSStatus
  provider: SMSProvider
  messageId?: string
  error?: string
  sentAt?: Date
  deliveredAt?: Date
  createdAt: Date
}

export enum SMSStatus {
  PENDING = 'pending',
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum SMSProvider {
  CLICKATELL = 'clickatell',
  TWILIO = 'twilio',
  BULK_SMS = 'bulk_sms'
}

// Push Notifications
export interface PushNotificationRequest {
  to: string | string[] // Device tokens or user IDs
  title: string
  body: string
  data?: Record<string, any>
  icon?: string
  image?: string
  badge?: number
  sound?: string
  clickAction?: string
  priority?: PushPriority
  scheduledAt?: Date
}

export enum PushPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high'
}

export interface PushNotificationLog {
  id: string
  to: string[]
  title: string
  body: string
  data?: Record<string, any>
  status: PushStatus
  provider: PushProvider
  messageId?: string
  error?: string
  sentAt?: Date
  deliveredAt?: Date
  clickedAt?: Date
  createdAt: Date
}

export enum PushStatus {
  PENDING = 'pending',
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  CLICKED = 'clicked',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum PushProvider {
  FCM = 'fcm',
  APNS = 'apns',
  WEB_PUSH = 'web_push'
}

// Notification Events
export interface NotificationEvent {
  id: string
  type: NotificationEventType
  data: any
  triggeredAt: Date
  processedAt?: Date
  status: NotificationEventStatus
}

export enum NotificationEventType {
  ORDER_CREATED = 'order_created',
  ORDER_UPDATED = 'order_updated',
  ORDER_CANCELLED = 'order_cancelled',
  PAYMENT_COMPLETED = 'payment_completed',
  PAYMENT_FAILED = 'payment_failed',
  SHIPMENT_CREATED = 'shipment_created',
  SHIPMENT_DELIVERED = 'shipment_delivered',
  CUSTOMER_REGISTERED = 'customer_registered',
  PASSWORD_RESET = 'password_reset',
  STOCK_LOW = 'stock_low',
  STOCK_OUT = 'stock_out',
  REVIEW_SUBMITTED = 'review_submitted',
  NEWSLETTER_SIGNUP = 'newsletter_signup',
  ABANDONED_CART = 'abandoned_cart'
}

export enum NotificationEventStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped'
}

// Notification Rules
export interface NotificationRule {
  id: string
  name: string
  description: string
  eventType: NotificationEventType
  conditions: NotificationCondition[]
  actions: NotificationAction[]
  isActive: boolean
  priority: number
  createdAt: Date
  updatedAt: Date
}

export interface NotificationCondition {
  field: string
  operator: ConditionOperator
  value: any
  type: 'string' | 'number' | 'boolean' | 'date'
}

export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  IN = 'in',
  NOT_IN = 'not_in'
}

export interface NotificationAction {
  type: NotificationActionType
  config: any
  delay?: number // seconds
}

export enum NotificationActionType {
  SEND_EMAIL = 'send_email',
  SEND_SMS = 'send_sms',
  SEND_PUSH = 'send_push',
  CREATE_TASK = 'create_task',
  UPDATE_STATUS = 'update_status',
  WEBHOOK = 'webhook'
}

// Notification Preferences
export interface NotificationPreferences {
  userId: string
  email: {
    orderUpdates: boolean
    paymentNotifications: boolean
    shippingUpdates: boolean
    promotions: boolean
    newsletters: boolean
  }
  sms: {
    orderUpdates: boolean
    paymentNotifications: boolean
    shippingUpdates: boolean
    urgentAlerts: boolean
  }
  push: {
    orderUpdates: boolean
    paymentNotifications: boolean
    shippingUpdates: boolean
    promotions: boolean
    generalNotifications: boolean
  }
  frequency: NotificationFrequency
  timezone: string
  language: string
}

export enum NotificationFrequency {
  IMMEDIATE = 'immediate',
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  NEVER = 'never'
}

// Notification Analytics
export interface NotificationAnalytics {
  totalSent: number
  totalDelivered: number
  totalOpened: number
  totalClicked: number
  totalBounced: number
  deliveryRate: number
  openRate: number
  clickRate: number
  bounceRate: number
  
  byChannel: Array<{
    channel: 'email' | 'sms' | 'push'
    sent: number
    delivered: number
    opened?: number
    clicked?: number
    bounced?: number
  }>
  
  byTemplate: Array<{
    templateId: string
    templateName: string
    sent: number
    delivered: number
    opened?: number
    clicked?: number
  }>
  
  byTimeOfDay: Array<{
    hour: number
    sent: number
    opened?: number
    clicked?: number
  }>
}

// Provider Configurations
export interface EmailProviderConfig {
  smtp?: {
    host: string
    port: number
    secure: boolean
    username: string
    password: string
  }
  sendgrid?: {
    apiKey: string
    fromEmail: string
    fromName: string
  }
  mailgun?: {
    apiKey: string
    domain: string
    fromEmail: string
    fromName: string
  }
}

export interface SMSProviderConfig {
  clickatell?: {
    apiKey: string
    fromNumber: string
  }
  twilio?: {
    accountSid: string
    authToken: string
    fromNumber: string
  }
  bulkSms?: {
    username: string
    password: string
    fromNumber: string
  }
}

export interface PushProviderConfig {
  fcm?: {
    serverKey: string
    senderId: string
  }
  apns?: {
    keyId: string
    teamId: string
    bundleId: string
    privateKey: string
    production: boolean
  }
}

export interface NotificationConfig {
  email: EmailProviderConfig
  sms: SMSProviderConfig
  push: PushProviderConfig
  defaultFromEmail: string
  defaultFromName: string
  defaultFromNumber: string
  retryAttempts: number
  retryDelay: number
  batchSize: number
  rateLimitPerMinute: number
}

// Core Enums
export enum NotificationType {
  ORDER_CONFIRMATION = 'ORDER_CONFIRMATION',
  ORDER_STATUS_UPDATE = 'ORDER_STATUS_UPDATE',
  PAYMENT_CONFIRMATION = 'PAYMENT_CONFIRMATION',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  SHIPPING_UPDATE = 'SHIPPING_UPDATE',
  DELIVERY_CONFIRMATION = 'DELIVERY_CONFIRMATION',
  PROMOTIONAL = 'PROMOTIONAL',
  NEWSLETTER = 'NEWSLETTER',
  WELCOME = 'WELCOME',
  PASSWORD_RESET = 'PASSWORD_RESET',
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',
  CUSTOM = 'CUSTOM'
}

export enum NotificationChannel {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  PUSH = 'PUSH',
  IN_APP = 'IN_APP',
  WEBHOOK = 'WEBHOOK'
}

export enum NotificationStatus {
  PENDING = 'PENDING',
  QUEUED = 'QUEUED',
  SENDING = 'SENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
  CLICKED = 'CLICKED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED'
}

export enum NotificationPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum RecipientType {
  CUSTOMER = 'CUSTOMER',
  ADMIN = 'ADMIN'
}

export enum CampaignType {
  BROADCAST = 'broadcast',
  TARGETED = 'targeted',
  AUTOMATED = 'automated',
  TRANSACTIONAL = 'transactional'
}

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// Database Models
export interface NotificationRecord {
  id: string
  type: NotificationType
  channel: NotificationChannel
  title: string
  content: string
  data?: Record<string, any>
  recipientId?: string
  recipientType?: RecipientType
  recipientEmail?: string
  recipientPhone?: string
  status: NotificationStatus
  priority: NotificationPriority
  templateId?: string
  campaignId?: string
  scheduledAt?: Date
  sentAt?: Date
  deliveredAt?: Date
  readAt?: Date
  clickedAt?: Date
  failedAt?: Date
  retryCount: number
  maxRetries: number
  error?: string
  metadata?: Record<string, any>
  expiresAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface NotificationTemplateRecord {
  id: string
  name: string
  description?: string
  type: NotificationType
  channel: NotificationChannel
  subject?: string
  content: string
  htmlContent?: string
  variables: string[]
  isActive: boolean
  isSystem: boolean
  category?: string
  tags: string[]
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface NotificationCampaignRecord {
  id: string
  name: string
  description?: string
  type: CampaignType
  status: CampaignStatus
  templateId: string
  targetAudience: Record<string, any>
  scheduledAt?: Date
  startedAt?: Date
  completedAt?: Date
  pausedAt?: Date
  totalRecipients: number
  sentCount: number
  deliveredCount: number
  failedCount: number
  openedCount: number
  clickedCount: number
  unsubscribedCount: number
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface NotificationPreferenceRecord {
  id: string
  userId: string
  userType: RecipientType
  emailEnabled: boolean
  smsEnabled: boolean
  pushEnabled: boolean
  inAppEnabled: boolean
  categories: Record<string, any>
  frequency: NotificationFrequency
  quietHoursStart?: string
  quietHoursEnd?: string
  timezone: string
  language: string
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

// Request/Response Types
export interface NotificationRequest {
  type: NotificationType
  channel: NotificationChannel
  title: string
  content: string
  recipientId?: string
  recipientType?: RecipientType
  recipientEmail?: string
  recipientPhone?: string
  data?: Record<string, any>
  templateId?: string
  priority?: NotificationPriority
  scheduledAt?: Date
  expiresAt?: Date
  metadata?: Record<string, any>
}

export interface NotificationResult {
  success: boolean
  notificationId?: string
  messageId?: string
  error?: string
  metadata?: Record<string, any>
}

// Service Interfaces
export interface NotificationService {
  send(notification: NotificationRequest): Promise<NotificationResult>
  validateConfig(): Promise<boolean>
  getDeliveryStatus(messageId: string): Promise<DeliveryStatus>
}

export interface TemplateEngine {
  render(template: NotificationTemplateRecord, variables: Record<string, any>): Promise<RenderedTemplate>
  validate(template: NotificationTemplateRecord): Promise<ValidationResult>
  getVariables(content: string): string[]
}

export interface NotificationQueue {
  enqueue(notification: NotificationRecord): Promise<void>
  dequeue(): Promise<NotificationRecord | null>
  retry(notificationId: string): Promise<void>
  cancel(notificationId: string): Promise<void>
  getStats(): Promise<QueueStats>
}

// Additional Types
export interface RenderedTemplate {
  subject?: string
  content: string
  htmlContent?: string
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export interface QueueStats {
  pending: number
  processing: number
  completed: number
  failed: number
}

export interface DeliveryStatus {
  status: NotificationStatus
  timestamp?: Date
  error?: string
  metadata?: Record<string, any>
}



// Type aliases for backward compatibility
export type InAppNotificationStatus = 'unread' | 'read' | 'archived'
export type PushNotificationProvider = 'firebase' | 'apns' | 'web-push' | 'custom'
export type PushNotificationStatus = 'pending' | 'sent' | 'delivered' | 'failed' | 'clicked'

// All types and enums are already exported above
