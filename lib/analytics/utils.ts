// Analytics utility functions
import { AnalyticsEvent } from './manager'

export interface MetricData {
  name: string
  value: number
  timestamp: Date
  tags?: Record<string, string>
}

export interface PerformanceMetrics {
  pageLoadTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  cumulativeLayoutShift: number
  firstInputDelay: number
}

// Performance monitoring utilities
export function measurePerformance(): PerformanceMetrics | null {
  if (typeof window === 'undefined' || !window.performance) {
    return null
  }

  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  const paint = performance.getEntriesByType('paint')
  
  const fcp = paint.find(entry => entry.name === 'first-contentful-paint')
  const lcp = performance.getEntriesByType('largest-contentful-paint')[0]

  return {
    pageLoadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
    firstContentfulPaint: fcp ? fcp.startTime : 0,
    largestContentfulPaint: lcp ? lcp.startTime : 0,
    cumulativeLayoutShift: 0, // Would need to implement CLS measurement
    firstInputDelay: 0 // Would need to implement FID measurement
  }
}

// Event aggregation utilities
export function aggregateEvents(events: AnalyticsEvent[]): Record<string, number> {
  const aggregated: Record<string, number> = {}
  
  events.forEach(event => {
    aggregated[event.name] = (aggregated[event.name] || 0) + 1
  })
  
  return aggregated
}

export function getEventsByTimeRange(
  events: AnalyticsEvent[], 
  startTime: Date, 
  endTime: Date
): AnalyticsEvent[] {
  return events.filter(event => {
    const eventTime = event.timestamp || new Date()
    return eventTime >= startTime && eventTime <= endTime
  })
}

export function getTopEvents(events: AnalyticsEvent[], limit: number = 10): Array<{name: string, count: number}> {
  const aggregated = aggregateEvents(events)
  
  return Object.entries(aggregated)
    .sort(([, a], [, b]) => b - a)
    .slice(0, limit)
    .map(([name, count]) => ({ name, count }))
}

// User behavior analysis
export function calculateSessionDuration(events: AnalyticsEvent[]): number {
  if (events.length === 0) return 0
  
  const timestamps = events
    .map(e => e.timestamp || new Date())
    .sort((a, b) => a.getTime() - b.getTime())
  
  const first = timestamps[0]
  const last = timestamps[timestamps.length - 1]
  
  return last.getTime() - first.getTime()
}

export function getUniqueUsers(events: AnalyticsEvent[]): string[] {
  const users = new Set<string>()
  
  events.forEach(event => {
    if (event.userId) {
      users.add(event.userId)
    }
  })
  
  return Array.from(users)
}

export function getPageViews(events: AnalyticsEvent[]): Array<{path: string, count: number}> {
  const pageViews: Record<string, number> = {}
  
  events
    .filter(event => event.name === 'page_view')
    .forEach(event => {
      const path = event.properties?.path || 'unknown'
      pageViews[path] = (pageViews[path] || 0) + 1
    })
  
  return Object.entries(pageViews)
    .sort(([, a], [, b]) => b - a)
    .map(([path, count]) => ({ path, count }))
}

// Conversion tracking utilities
export function calculateConversionRate(
  events: AnalyticsEvent[], 
  conversionEvent: string,
  totalEvents?: string
): number {
  const conversions = events.filter(e => e.name === conversionEvent).length
  const total = totalEvents 
    ? events.filter(e => e.name === totalEvents).length
    : getUniqueUsers(events).length
  
  return total > 0 ? (conversions / total) * 100 : 0
}

// Data export utilities
export function exportEventsToCSV(events: AnalyticsEvent[]): string {
  if (events.length === 0) return ''
  
  const headers = ['timestamp', 'name', 'userId', 'sessionId', 'properties']
  const rows = events.map(event => [
    event.timestamp?.toISOString() || '',
    event.name,
    event.userId || '',
    event.sessionId || '',
    JSON.stringify(event.properties || {})
  ])
  
  return [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n')
}

export function exportEventsToJSON(events: AnalyticsEvent[]): string {
  return JSON.stringify(events, null, 2)
}

// Real-time analytics utilities
export function createEventStream() {
  const listeners: Array<(event: AnalyticsEvent) => void> = []
  
  return {
    subscribe: (callback: (event: AnalyticsEvent) => void) => {
      listeners.push(callback)
      return () => {
        const index = listeners.indexOf(callback)
        if (index > -1) {
          listeners.splice(index, 1)
        }
      }
    },
    emit: (event: AnalyticsEvent) => {
      listeners.forEach(callback => callback(event))
    }
  }
}

// Privacy utilities
export function anonymizeEvent(event: AnalyticsEvent): AnalyticsEvent {
  const anonymized = { ...event }
  
  // Remove or hash sensitive data
  if (anonymized.properties) {
    const { email, phone, address, ...safeProperties } = anonymized.properties
    anonymized.properties = safeProperties
  }
  
  // Hash user ID if present
  if (anonymized.userId) {
    anonymized.userId = hashString(anonymized.userId)
  }
  
  return anonymized
}

function hashString(str: string): string {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return hash.toString(36)
}

// Date range utilities
export interface DateRange {
  start: Date
  end: Date
}

export function generateDateRange(period: string): DateRange {
  const now = new Date()
  const start = new Date()

  switch (period) {
    case 'today':
      start.setHours(0, 0, 0, 0)
      break
    case 'yesterday':
      start.setDate(now.getDate() - 1)
      start.setHours(0, 0, 0, 0)
      now.setDate(now.getDate() - 1)
      now.setHours(23, 59, 59, 999)
      break
    case 'last7days':
      start.setDate(now.getDate() - 7)
      break
    case 'last30days':
      start.setDate(now.getDate() - 30)
      break
    case 'last90days':
      start.setDate(now.getDate() - 90)
      break
    case 'thisMonth':
      start.setDate(1)
      start.setHours(0, 0, 0, 0)
      break
    case 'lastMonth':
      start.setMonth(now.getMonth() - 1, 1)
      start.setHours(0, 0, 0, 0)
      now.setDate(0) // Last day of previous month
      now.setHours(23, 59, 59, 999)
      break
    case 'thisYear':
      start.setMonth(0, 1)
      start.setHours(0, 0, 0, 0)
      break
    default:
      start.setDate(now.getDate() - 30)
  }

  return { start, end: now }
}
