'use client'

import { useState, useEffect, useCallback } from 'react'
import { PermissionCheck, PermissionResult, PermissionAction } from '../../posts/types'

interface UsePermissionsOptions {
  userId?: string
  autoCheck?: boolean
}

export function usePermissions(options: UsePermissionsOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [permissions, setPermissions] = useState<Record<string, PermissionResult>>({})

  /**
   * Check a specific permission
   */
  const checkPermission = useCallback(async (
    resource: string,
    action: PermissionAction,
    resourceId?: string,
    context?: Record<string, any>
  ): Promise<PermissionResult> => {
    if (!options.userId) {
      return { allowed: false, reason: 'User not authenticated' }
    }

    const cacheKey = `${resource}:${action}:${resourceId || 'none'}`
    
    // Return cached result if available
    if (permissions[cacheKey]) {
      return permissions[cacheKey]
    }

    setIsLoading(true)

    try {
      const permissionCheck: PermissionCheck = {
        resource,
        action,
        resourceId,
        userId: options.userId,
        context
      }

      const response = await fetch('/api/auth/permissions/check', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(permissionCheck)
      })

      const result = await response.json()

      if (result.success) {
        // Cache the result
        setPermissions(prev => ({
          ...prev,
          [cacheKey]: result.data
        }))
        return result.data
      } else {
        const errorResult: PermissionResult = { 
          allowed: false, 
          reason: result.error || 'Permission check failed' 
        }
        setPermissions(prev => ({
          ...prev,
          [cacheKey]: errorResult
        }))
        return errorResult
      }
    } catch (error) {
      console.error('Error checking permission:', error)
      const errorResult: PermissionResult = { 
        allowed: false, 
        reason: 'Permission check failed' 
      }
      setPermissions(prev => ({
        ...prev,
        [cacheKey]: errorResult
      }))
      return errorResult
    } finally {
      setIsLoading(false)
    }
  }, [options.userId, permissions])

  /**
   * Check multiple permissions at once
   */
  const checkPermissions = useCallback(async (
    checks: Array<{
      resource: string
      action: PermissionAction
      resourceId?: string
      context?: Record<string, any>
    }>
  ): Promise<Record<string, PermissionResult>> => {
    const results: Record<string, PermissionResult> = {}

    await Promise.all(
      checks.map(async (check) => {
        const key = `${check.resource}:${check.action}:${check.resourceId || 'none'}`
        results[key] = await checkPermission(
          check.resource,
          check.action,
          check.resourceId,
          check.context
        )
      })
    )

    return results
  }, [checkPermission])

  /**
   * Check if user can perform action on resource
   */
  const can = useCallback((
    resource: string,
    action: PermissionAction,
    resourceId?: string
  ): boolean => {
    const cacheKey = `${resource}:${action}:${resourceId || 'none'}`
    return permissions[cacheKey]?.allowed || false
  }, [permissions])

  /**
   * Check if user cannot perform action on resource
   */
  const cannot = useCallback((
    resource: string,
    action: PermissionAction,
    resourceId?: string
  ): boolean => {
    return !can(resource, action, resourceId)
  }, [can])

  /**
   * Clear permission cache
   */
  const clearCache = useCallback(() => {
    setPermissions({})
  }, [])

  /**
   * Preload common permissions
   */
  const preloadPermissions = useCallback(async (
    commonChecks: Array<{
      resource: string
      action: PermissionAction
      resourceId?: string
    }>
  ) => {
    await checkPermissions(commonChecks)
  }, [checkPermissions])

  return {
    checkPermission,
    checkPermissions,
    can,
    cannot,
    clearCache,
    preloadPermissions,
    isLoading,
    permissions
  }
}

/**
 * Hook for checking a single permission with automatic loading
 */
export function usePermission(
  resource: string,
  action: PermissionAction,
  resourceId?: string,
  userId?: string
) {
  const [result, setResult] = useState<PermissionResult | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!userId) {
      setResult({ allowed: false, reason: 'User not authenticated' })
      setIsLoading(false)
      return
    }

    const checkPermission = async () => {
      setIsLoading(true)
      try {
        const response = await fetch(
          `/api/auth/permissions/check?resource=${encodeURIComponent(resource)}&action=${encodeURIComponent(action)}&userId=${encodeURIComponent(userId)}${resourceId ? `&resourceId=${encodeURIComponent(resourceId)}` : ''}`,
          { method: 'GET' }
        )

        const apiResult = await response.json()

        if (apiResult.success) {
          setResult(apiResult.data)
        } else {
          setResult({ allowed: false, reason: apiResult.error || 'Permission check failed' })
        }
      } catch (error) {
        console.error('Error checking permission:', error)
        setResult({ allowed: false, reason: 'Permission check failed' })
      } finally {
        setIsLoading(false)
      }
    }

    checkPermission()
  }, [resource, action, resourceId, userId])

  return {
    allowed: result?.allowed || false,
    reason: result?.reason,
    restrictions: result?.restrictions,
    isLoading,
    result
  }
}

/**
 * Higher-order component for permission-based rendering
 */
export function withPermission<T extends object>(
  Component: React.ComponentType<T>,
  resource: string,
  action: PermissionAction,
  fallback?: React.ComponentType<T> | React.ReactNode
) {
  return function PermissionWrappedComponent(props: T & { userId?: string; resourceId?: string }) {
    const { userId, resourceId, ...componentProps } = props
    const { allowed, isLoading } = usePermission(resource, action, resourceId, userId)

    if (isLoading) {
      return <div className="animate-pulse bg-gray-200 rounded h-8 w-32"></div>
    }

    if (!allowed) {
      if (fallback) {
        if (React.isValidElement(fallback)) {
          return fallback
        }
        const FallbackComponent = fallback as React.ComponentType<T>
        return <FallbackComponent {...(componentProps as T)} />
      }
      return null
    }

    return <Component {...(componentProps as T)} />
  }
}

/**
 * Component for conditional rendering based on permissions
 */
interface PermissionGateProps {
  resource: string
  action: PermissionAction
  resourceId?: string
  userId?: string
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function PermissionGate({
  resource,
  action,
  resourceId,
  userId,
  fallback,
  children
}: PermissionGateProps) {
  const { allowed, isLoading } = usePermission(resource, action, resourceId, userId)

  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 rounded h-8 w-32"></div>
  }

  if (!allowed) {
    return fallback || null
  }

  return <>{children}</>
}
