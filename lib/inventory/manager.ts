// Inventory Management System
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export interface InventoryAlert {
  id: string
  type: 'low_stock' | 'out_of_stock' | 'overstock'
  productId: string
  variantId?: string
  message: string
  threshold: number
  currentLevel: number
  isActive: boolean
  createdAt: Date
}

export interface StockLevel {
  productId: string
  variantId?: string
  quantity: number
  reserved: number
  available: number
  reorderPoint: number
  maxStock: number
}

export class InventoryManager {
  async getStockLevels(): Promise<StockLevel[]> {
    try {
      const inventoryItems = await prisma.inventoryItem.findMany({
        include: {
          product: true,
          variant: true
        }
      })

      return inventoryItems.map(item => ({
        productId: item.productId,
        variantId: item.variantId || undefined,
        quantity: item.quantity,
        reserved: item.reservedQuantity || 0,
        available: item.availableQuantity || item.quantity,
        reorderPoint: item.reorderPoint || 10,
        maxStock: item.maxStock || 1000
      }))
    } catch (error) {
      console.error('Error getting stock levels:', error)
      return []
    }
  }

  async getLowStockAlerts(): Promise<InventoryAlert[]> {
    try {
      const alerts = await prisma.inventoryAlert.findMany({
        where: {
          isActive: true,
          type: 'low_stock'
        },
        include: {
          inventoryItem: {
            include: {
              product: true,
              variant: true
            }
          }
        }
      })

      return alerts.map(alert => ({
        id: alert.id,
        type: alert.type as 'low_stock' | 'out_of_stock' | 'overstock',
        productId: alert.inventoryItem.productId,
        variantId: alert.inventoryItem.variantId || undefined,
        message: alert.message,
        threshold: alert.threshold,
        currentLevel: alert.inventoryItem.quantity,
        isActive: alert.isActive,
        createdAt: alert.createdAt
      }))
    } catch (error) {
      console.error('Error getting low stock alerts:', error)
      return []
    }
  }

  async checkStockLevel(productId: string, variantId?: string): Promise<{
    inStock: boolean
    quantity: number
    available: number
  }> {
    try {
      const inventoryItem = await prisma.inventoryItem.findFirst({
        where: {
          productId,
          ...(variantId && { variantId })
        }
      })

      if (!inventoryItem) {
        return {
          inStock: false,
          quantity: 0,
          available: 0
        }
      }

      const available = inventoryItem.availableQuantity || inventoryItem.quantity
      
      return {
        inStock: available > 0,
        quantity: inventoryItem.quantity,
        available
      }
    } catch (error) {
      console.error('Error checking stock level:', error)
      return {
        inStock: false,
        quantity: 0,
        available: 0
      }
    }
  }

  async updateStock(
    productId: string, 
    quantity: number, 
    variantId?: string,
    reason?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const inventoryItem = await prisma.inventoryItem.findFirst({
        where: {
          productId,
          ...(variantId && { variantId })
        }
      })

      if (!inventoryItem) {
        return {
          success: false,
          error: 'Inventory item not found'
        }
      }

      // Update inventory
      await prisma.inventoryItem.update({
        where: { id: inventoryItem.id },
        data: {
          quantity,
          availableQuantity: quantity - (inventoryItem.reservedQuantity || 0),
          updatedAt: new Date()
        }
      })

      // Create movement record
      await prisma.inventoryMovement.create({
        data: {
          inventoryItemId: inventoryItem.id,
          type: quantity > inventoryItem.quantity ? 'adjustment_in' : 'adjustment_out',
          quantity: Math.abs(quantity - inventoryItem.quantity),
          reason: reason || 'Manual adjustment',
          createdAt: new Date()
        }
      })

      // Check for alerts
      await this.checkAndCreateAlerts(inventoryItem.id)

      return { success: true }
    } catch (error) {
      console.error('Error updating stock:', error)
      return {
        success: false,
        error: 'Failed to update stock'
      }
    }
  }

  async reserveStock(
    productId: string,
    quantity: number,
    variantId?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const inventoryItem = await prisma.inventoryItem.findFirst({
        where: {
          productId,
          ...(variantId && { variantId })
        }
      })

      if (!inventoryItem) {
        return {
          success: false,
          error: 'Inventory item not found'
        }
      }

      const available = inventoryItem.availableQuantity || inventoryItem.quantity
      if (available < quantity) {
        return {
          success: false,
          error: 'Insufficient stock available'
        }
      }

      await prisma.inventoryItem.update({
        where: { id: inventoryItem.id },
        data: {
          reservedQuantity: (inventoryItem.reservedQuantity || 0) + quantity,
          availableQuantity: available - quantity
        }
      })

      return { success: true }
    } catch (error) {
      console.error('Error reserving stock:', error)
      return {
        success: false,
        error: 'Failed to reserve stock'
      }
    }
  }

  async releaseReservedStock(
    productId: string,
    quantity: number,
    variantId?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const inventoryItem = await prisma.inventoryItem.findFirst({
        where: {
          productId,
          ...(variantId && { variantId })
        }
      })

      if (!inventoryItem) {
        return {
          success: false,
          error: 'Inventory item not found'
        }
      }

      const newReserved = Math.max(0, (inventoryItem.reservedQuantity || 0) - quantity)
      const newAvailable = inventoryItem.quantity - newReserved

      await prisma.inventoryItem.update({
        where: { id: inventoryItem.id },
        data: {
          reservedQuantity: newReserved,
          availableQuantity: newAvailable
        }
      })

      return { success: true }
    } catch (error) {
      console.error('Error releasing reserved stock:', error)
      return {
        success: false,
        error: 'Failed to release reserved stock'
      }
    }
  }

  private async checkAndCreateAlerts(inventoryItemId: string): Promise<void> {
    try {
      const inventoryItem = await prisma.inventoryItem.findUnique({
        where: { id: inventoryItemId },
        include: {
          product: true,
          variant: true
        }
      })

      if (!inventoryItem) return

      const reorderPoint = inventoryItem.reorderPoint || 10
      const available = inventoryItem.availableQuantity || inventoryItem.quantity

      // Check for low stock
      if (available <= reorderPoint && available > 0) {
        await this.createAlert(inventoryItemId, 'low_stock', reorderPoint, available)
      }

      // Check for out of stock
      if (available <= 0) {
        await this.createAlert(inventoryItemId, 'out_of_stock', 0, available)
      }
    } catch (error) {
      console.error('Error checking alerts:', error)
    }
  }

  private async createAlert(
    inventoryItemId: string,
    type: 'low_stock' | 'out_of_stock' | 'overstock',
    threshold: number,
    currentLevel: number
  ): Promise<void> {
    try {
      // Check if alert already exists
      const existingAlert = await prisma.inventoryAlert.findFirst({
        where: {
          inventoryItemId,
          type,
          isActive: true
        }
      })

      if (existingAlert) return

      await prisma.inventoryAlert.create({
        data: {
          inventoryItemId,
          type,
          message: this.generateAlertMessage(type, currentLevel),
          threshold,
          isActive: true
        }
      })
    } catch (error) {
      console.error('Error creating alert:', error)
    }
  }

  private generateAlertMessage(type: string, currentLevel: number): string {
    switch (type) {
      case 'low_stock':
        return `Low stock alert: Only ${currentLevel} items remaining`
      case 'out_of_stock':
        return 'Out of stock: Item is no longer available'
      case 'overstock':
        return `Overstock alert: ${currentLevel} items exceed maximum capacity`
      default:
        return 'Inventory alert'
    }
  }
}

export const inventoryManager = new InventoryManager()
