// Settings Service for managing application configuration
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export interface SettingValue {
  id: string
  key: string
  value: any
  type: 'json' | 'string' | 'number' | 'boolean'
  category: string
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
}

export interface PaymentSettings {
  payfast: {
    enabled: boolean
    merchantId: string
    merchantKey: string
    passphrase: string
    testMode: boolean
    status: 'connected' | 'disconnected' | 'testing'
  }
  ozow: {
    enabled: boolean
    siteCode: string
    privateKey: string
    apiKey: string
    testMode: boolean
    bankReference: string
    status: 'connected' | 'disconnected' | 'testing'
  }
  stripe: {
    enabled: boolean
    publishableKey: string
    secretKey: string
    webhookSecret: string
    currency: string
    testMode: boolean
    status: 'connected' | 'disconnected' | 'testing'
  }
  paypal: {
    enabled: boolean
    clientId: string
    clientSecret: string
    mode: 'sandbox' | 'live'
    testMode: boolean
    status: 'connected' | 'disconnected' | 'testing'
  }
  cod: {
    enabled: boolean
    title: string
    description: string
    instructions: string
    enableForShippingMethods: string[]
  }
}

export class SettingsService {
  async getSetting(key: string): Promise<SettingValue | null> {
    try {
      // Check if we're in a build environment or if prisma is not available
      if (!prisma || typeof window === 'undefined' && process.env.NODE_ENV === 'production') {
        console.warn('Database not available during build, returning null for setting:', key)
        return null
      }

      // Additional check for database connection
      if (!process.env.DATABASE_URL) {
        console.warn('DATABASE_URL not configured, returning null for setting:', key)
        return null
      }

      const setting = await prisma.setting.findUnique({
        where: { key }
      })

      if (!setting) return null

      return {
        id: setting.id,
        key: setting.key,
        value: setting.value,
        type: setting.type as any,
        category: setting.category,
        isPublic: setting.isPublic,
        createdAt: setting.createdAt,
        updatedAt: setting.updatedAt
      }
    } catch (error) {
      console.error('Error getting setting:', error)
      return null
    }
  }

  async setSetting(
    key: string,
    value: any,
    type: 'json' | 'string' | 'number' | 'boolean' = 'json',
    category: string = 'general',
    isPublic: boolean = false
  ): Promise<SettingValue | null> {
    try {
      // Check if we're in a build environment or if prisma is not available
      if (!prisma || process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
        return null
      }

      const setting = await prisma.setting.upsert({
        where: { key },
        update: {
          value,
          type,
          category,
          isPublic,
          updatedAt: new Date()
        },
        create: {
          key,
          value,
          type,
          category,
          isPublic
        }
      })

      return {
        id: setting.id,
        key: setting.key,
        value: setting.value,
        type: setting.type as any,
        category: setting.category,
        isPublic: setting.isPublic,
        createdAt: setting.createdAt,
        updatedAt: setting.updatedAt
      }
    } catch (error) {
      console.error('Error setting value:', error)
      return null
    }
  }

  async getPaymentSettings(): Promise<PaymentSettings> {
    try {
      // During build time or when database is not available, return defaults
      if (!prisma || typeof window === 'undefined' && process.env.NODE_ENV === 'production') {
        console.warn('Database not available during build, returning default payment settings')
        return this.getDefaultPaymentSettings()
      }

      const setting = await this.getSetting('payments')

      if (setting?.value) {
        return setting.value as PaymentSettings
      }

      // Return default payment settings
      return this.getDefaultPaymentSettings()
    } catch (error) {
      console.error('Error getting payment settings:', error)
      return this.getDefaultPaymentSettings()
    }
  }

  async setPaymentSettings(settings: Partial<PaymentSettings>): Promise<boolean> {
    try {
      const currentSettings = await this.getPaymentSettings()
      const updatedSettings = { ...currentSettings, ...settings }

      const result = await this.setSetting(
        'payments',
        updatedSettings,
        'json',
        'payments',
        false
      )

      return result !== null
    } catch (error) {
      console.error('Error setting payment settings:', error)
      return false
    }
  }

  async updatePaymentGateway(
    gateway: keyof PaymentSettings,
    config: Partial<PaymentSettings[keyof PaymentSettings]>
  ): Promise<boolean> {
    try {
      const currentSettings = await this.getPaymentSettings()
      
      currentSettings[gateway] = {
        ...currentSettings[gateway],
        ...config
      } as any

      return await this.setPaymentSettings(currentSettings)
    } catch (error) {
      console.error('Error updating payment gateway:', error)
      return false
    }
  }

  async getSettingsByCategory(category: string): Promise<SettingValue[]> {
    try {
      const settings = await prisma.setting.findMany({
        where: { category }
      })

      return settings.map(setting => ({
        id: setting.id,
        key: setting.key,
        value: setting.value,
        type: setting.type as any,
        category: setting.category,
        isPublic: setting.isPublic,
        createdAt: setting.createdAt,
        updatedAt: setting.updatedAt
      }))
    } catch (error) {
      console.error('Error getting settings by category:', error)
      return []
    }
  }

  async getPublicSettings(): Promise<Record<string, any>> {
    try {
      const settings = await prisma.setting.findMany({
        where: { isPublic: true }
      })

      const publicSettings: Record<string, any> = {}
      settings.forEach(setting => {
        publicSettings[setting.key] = setting.value
      })

      return publicSettings
    } catch (error) {
      console.error('Error getting public settings:', error)
      return {}
    }
  }

  async deleteSetting(key: string): Promise<boolean> {
    try {
      await prisma.setting.delete({
        where: { key }
      })
      return true
    } catch (error) {
      console.error('Error deleting setting:', error)
      return false
    }
  }

  private getDefaultPaymentSettings(): PaymentSettings {
    return {
      payfast: {
        enabled: true,
        merchantId: '********', // Default test merchant ID
        merchantKey: '46f0cd694581a', // Default test merchant key
        passphrase: '',
        testMode: true,
        status: 'connected'
      },
      ozow: {
        enabled: true,
        siteCode: 'TEST-SITE',
        privateKey: '',
        apiKey: '',
        testMode: true,
        bankReference: 'Coco Milk Kids',
        status: 'connected'
      },
      stripe: {
        enabled: false,
        publishableKey: '',
        secretKey: '',
        webhookSecret: '',
        currency: 'ZAR',
        testMode: true,
        status: 'disconnected'
      },
      paypal: {
        enabled: false,
        clientId: '',
        clientSecret: '',
        mode: 'sandbox',
        testMode: true,
        status: 'disconnected'
      },
      cod: {
        enabled: true,
        title: 'Cash on Delivery',
        description: 'Pay when you receive your order',
        instructions: 'Please have the exact amount ready when the courier arrives.',
        enableForShippingMethods: ['standard', 'express']
      }
    }
  }

  // Initialize default settings if they don't exist
  async initializeDefaultSettings(): Promise<void> {
    try {
      const existingPaymentSettings = await this.getSetting('payments')
      
      if (!existingPaymentSettings) {
        await this.setPaymentSettings(this.getDefaultPaymentSettings())
      }

      // Initialize other default settings as needed
      const generalSettings = await this.getSetting('general')
      if (!generalSettings) {
        await this.setSetting('general', {
          siteName: 'Coco Milk Kids',
          siteDescription: 'Premium children\'s clothing and accessories',
          currency: 'ZAR',
          timezone: 'Africa/Johannesburg',
          language: 'en_ZA'
        }, 'json', 'general', true)
      }
    } catch (error) {
      console.error('Error initializing default settings:', error)
    }
  }
}

export const settingsService = new SettingsService()
