/**
 * Rate Limiting Utility
 *
 * Provides rate limiting functionality for API endpoints
 * Fallback implementation when Upstash is not available
 */

interface RateLimitResult {
  success: boolean
  limit: number
  remaining: number
  reset: Date
}

class SimpleRateLimit {
  private requests: Map<string, { count: number; resetTime: number }> = new Map()
  private limit: number
  private windowMs: number

  constructor(limit: number, windowMs: number) {
    this.limit = limit
    this.windowMs = windowMs
  }

  async limit(identifier: string): Promise<RateLimitResult> {
    const now = Date.now()
    const key = identifier
    const existing = this.requests.get(key)

    // Clean up expired entries
    if (existing && now > existing.resetTime) {
      this.requests.delete(key)
    }

    const current = this.requests.get(key) || { count: 0, resetTime: now + this.windowMs }

    if (current.count >= this.limit) {
      return {
        success: false,
        limit: this.limit,
        remaining: 0,
        reset: new Date(current.resetTime)
      }
    }

    current.count++
    this.requests.set(key, current)

    return {
      success: true,
      limit: this.limit,
      remaining: this.limit - current.count,
      reset: new Date(current.resetTime)
    }
  }
}

// Create rate limiter instances
export const ratelimit = new SimpleRateLimit(100, 60000) // 100 requests per minute

export const strictRatelimit = new SimpleRateLimit(10, 60000) // 10 requests per minute

export const adminRatelimit = new SimpleRateLimit(1000, 60000) // 1000 requests per minute for admins
