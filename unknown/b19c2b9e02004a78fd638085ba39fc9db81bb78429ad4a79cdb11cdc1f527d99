// React hooks for wishlist management
'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  Product,
  ApiResponse
} from '../types'

export interface WishlistItem {
  id: string
  productId: string
  variantId?: string
  product?: Product
  addedAt: Date
}

export interface UseWishlistOptions {
  userId?: string
  sessionId?: string
  autoFetch?: boolean
}

export interface UseWishlistReturn {
  items: WishlistItem[]
  productIds: string[]
  loading: boolean
  error: { code: string; message: string } | null
  isInWishlist: (productId: string, variantId?: string) => boolean
  addToWishlist: (productId: string, variantId?: string) => Promise<void>
  removeFromWishlist: (productId: string, variantId?: string) => Promise<void>
  toggleWishlist: (productId: string, variantId?: string) => Promise<void>
  clearWishlist: () => Promise<void>
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing customer wishlist
 */
export function useWishlist(options: UseWishlistOptions = {}): UseWishlistReturn {
  const { userId, sessionId, autoFetch = true } = options

  const [items, setItems] = useState<WishlistItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  // Memoized product IDs for quick lookup
  const productIds = items.map(item => item.productId)

  const fetchWishlist = useCallback(async () => {
    if (!userId && !sessionId) {
      // If no user or session, try to load from localStorage as fallback
      try {
        const storedWishlist = localStorage.getItem('wishlist')
        if (storedWishlist) {
          const localProductIds = JSON.parse(storedWishlist)
          if (Array.isArray(localProductIds)) {
            const localItems = localProductIds.map(productId => ({
              id: `local_${productId}`,
              productId,
              addedAt: new Date()
            }))
            setItems(localItems)
          }
        }
      } catch (err) {
        console.error('Failed to load wishlist from localStorage:', err)
      }
      return
    }

    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (userId) params.append('userId', userId)
      if (sessionId) params.append('sessionId', sessionId)

      const response = await fetch(`/api/e-commerce/wishlist?${params}`)
      const result: ApiResponse<WishlistItem[]> = await response.json()

      if (result.success && result.data) {
        setItems(result.data)
        
        // Sync with localStorage for offline access
        try {
          const productIds = result.data.map(item => item.productId)
          localStorage.setItem('wishlist', JSON.stringify(productIds))
        } catch (err) {
          console.error('Failed to sync wishlist to localStorage:', err)
        }
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch wishlist' })
        setItems([])
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setItems([])
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const isInWishlist = useCallback((productId: string, variantId?: string) => {
    return items.some(item => 
      item.productId === productId && 
      (!variantId || item.variantId === variantId)
    )
  }, [items])

  const addToWishlist = useCallback(async (productId: string, variantId?: string): Promise<void> => {
    // Check if already in wishlist
    if (isInWishlist(productId, variantId)) {
      return
    }

    // Optimistic update
    const tempItem: WishlistItem = {
      id: `temp_${Date.now()}`,
      productId,
      variantId,
      addedAt: new Date()
    }
    setItems(prev => [...prev, tempItem])

    try {
      if (userId || sessionId) {
        const response = await fetch('/api/e-commerce/wishlist', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            productId,
            variantId,
            userId,
            sessionId
          }),
        })

        const result: ApiResponse<WishlistItem> = await response.json()

        if (result.success && result.data) {
          // Replace temp item with real item
          setItems(prev => prev.map(item => 
            item.id === tempItem.id ? result.data! : item
          ))
        } else {
          // Revert optimistic update
          setItems(prev => prev.filter(item => item.id !== tempItem.id))
          setError(result.error || { code: 'ADD_ERROR', message: 'Failed to add to wishlist' })
        }
      } else {
        // Fallback to localStorage
        try {
          const storedWishlist = localStorage.getItem('wishlist')
          const currentIds = storedWishlist ? JSON.parse(storedWishlist) : []
          if (!currentIds.includes(productId)) {
            currentIds.push(productId)
            localStorage.setItem('wishlist', JSON.stringify(currentIds))
          }
        } catch (err) {
          console.error('Failed to save to localStorage:', err)
          // Revert optimistic update
          setItems(prev => prev.filter(item => item.id !== tempItem.id))
        }
      }
    } catch (err) {
      // Revert optimistic update
      setItems(prev => prev.filter(item => item.id !== tempItem.id))
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    }
  }, [userId, sessionId, isInWishlist])

  const removeFromWishlist = useCallback(async (productId: string, variantId?: string): Promise<void> => {
    const itemToRemove = items.find(item => 
      item.productId === productId && 
      (!variantId || item.variantId === variantId)
    )

    if (!itemToRemove) {
      return
    }

    // Optimistic update
    setItems(prev => prev.filter(item => item.id !== itemToRemove.id))

    try {
      if (userId || sessionId) {
        const params = new URLSearchParams()
        params.append('productId', productId)
        if (variantId) params.append('variantId', variantId)
        if (userId) params.append('userId', userId)
        if (sessionId) params.append('sessionId', sessionId)

        const response = await fetch(`/api/e-commerce/wishlist?${params}`, {
          method: 'DELETE'
        })

        const result: ApiResponse<boolean> = await response.json()

        if (!result.success) {
          // Revert optimistic update
          setItems(prev => [...prev, itemToRemove])
          setError(result.error || { code: 'REMOVE_ERROR', message: 'Failed to remove from wishlist' })
        }
      } else {
        // Fallback to localStorage
        try {
          const storedWishlist = localStorage.getItem('wishlist')
          const currentIds = storedWishlist ? JSON.parse(storedWishlist) : []
          const updatedIds = currentIds.filter((id: string) => id !== productId)
          localStorage.setItem('wishlist', JSON.stringify(updatedIds))
        } catch (err) {
          console.error('Failed to update localStorage:', err)
          // Revert optimistic update
          setItems(prev => [...prev, itemToRemove])
        }
      }
    } catch (err) {
      // Revert optimistic update
      setItems(prev => [...prev, itemToRemove])
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    }
  }, [items, userId, sessionId])

  const toggleWishlist = useCallback(async (productId: string, variantId?: string): Promise<void> => {
    if (isInWishlist(productId, variantId)) {
      await removeFromWishlist(productId, variantId)
    } else {
      await addToWishlist(productId, variantId)
    }
  }, [isInWishlist, addToWishlist, removeFromWishlist])

  const clearWishlist = useCallback(async (): Promise<void> => {
    // Optimistic update
    const previousItems = items
    setItems([])

    try {
      if (userId || sessionId) {
        const params = new URLSearchParams()
        if (userId) params.append('userId', userId)
        if (sessionId) params.append('sessionId', sessionId)

        const response = await fetch(`/api/e-commerce/wishlist/clear?${params}`, {
          method: 'DELETE'
        })

        const result: ApiResponse<boolean> = await response.json()

        if (!result.success) {
          // Revert optimistic update
          setItems(previousItems)
          setError(result.error || { code: 'CLEAR_ERROR', message: 'Failed to clear wishlist' })
        }
      }

      // Clear localStorage
      try {
        localStorage.removeItem('wishlist')
      } catch (err) {
        console.error('Failed to clear localStorage:', err)
      }
    } catch (err) {
      // Revert optimistic update
      setItems(previousItems)
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    }
  }, [items, userId, sessionId])

  const refetch = useCallback(() => {
    return fetchWishlist()
  }, [fetchWishlist])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      fetchWishlist()
    }
  }, [autoFetch, fetchWishlist])

  return {
    items,
    productIds,
    loading,
    error,
    isInWishlist,
    addToWishlist,
    removeFromWishlist,
    toggleWishlist,
    clearWishlist,
    refetch,
    clearError
  }
}
