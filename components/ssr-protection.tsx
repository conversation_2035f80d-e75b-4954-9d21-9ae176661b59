"use client"

import { useState, useEffect, ReactNode } from "react"
import { Loader2 } from "lucide-react"

interface SSRProtectionProps {
  children: ReactNode
  fallback?: ReactNode
  loadingMessage?: string
}

export function SSRProtection({ 
  children, 
  fallback, 
  loadingMessage = "Loading..." 
}: SSRProtectionProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) {
    return fallback || (
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <div className="flex items-center justify-center py-16">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">{loadingMessage}</span>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// Higher-order component version
export function withSSRProtection<P extends object>(
  Component: React.ComponentType<P>,
  loadingMessage?: string
) {
  return function WrappedComponent(props: P) {
    return (
      <SSRProtection loadingMessage={loadingMessage}>
        <Component {...props} />
      </SSRProtection>
    )
  }
}
